using System;
using Akka.Actor;
using Akka.IO;
using HeroYulgang.Core.Network;
using HeroYulgang.Helpers;
using HeroYulgang.Services;
using RxjhServer;

namespace HeroYulgang.Core.Actors
{
    /// <summary>
    /// Actor x<PERSON> lý kết nối của một client cụ thể
    /// </summary>
    public class ClientActor : ReceiveActor
    {
        private readonly IActorRef _connection;
        private readonly ClientSession _session;
        private readonly IActorRef _packetHandlerActor;
        private Players _player;
        private ActorNetState _actorNetState;

        public ClientActor(IActorRef connection, ClientSession session, IActorRef packetHandlerActor)
        {
            _connection = connection;
            _session = session;
            _packetHandlerActor = packetHandlerActor;
            // World.list.Add(session.SessionId, this);

            // Tạo ActorNetState cho kết nối này
            _actorNetState = PlayerNetworkManager.CreateActorNetState(_connection, _session.SessionId, _session.RemoteEndPoint);

            // <PERSON><PERSON><PERSON> nghĩa các message handler
            Receive<Tcp.Received>(HandleReceived);
            Receive<Tcp.ConnectionClosed>(HandleConnectionClosed);
            Receive<SetPlayerReference>(SetPlayerReference);
            Receive<SetPlayerReferenceAndLogin>(SetPlayerReferenceAndLogin);
        }

        private void SetPlayerReference(SetPlayerReference message)
        {
            _player = message.Player;

            // Thiết lập player context cho PacketHandlerActor thay vì tạo PlayerPacketHandlerActor riêng
            if (_player != null && _actorNetState != null)
            {
                Logger.Instance.Debug($"Player ID {_player.AccountID} (SessionID: {_player.SessionID}) đã kết nối {_actorNetState.SessionID} ");
                // Cập nhật Client của Player để sử dụng ActorNetState
                PlayerNetworkManager.UpdatePlayerClient(_player, _actorNetState);

                // Gửi player context đến PacketHandlerActor
                _packetHandlerActor.Tell(new SetPlayerContext(_player, _actorNetState, _connection));

                Logger.Instance.Debug($"Đã thiết lập player context cho PacketHandlerActor cho người chơi {_player.CharacterName}");
            }
        }

        private void SetPlayerReferenceAndLogin(SetPlayerReferenceAndLogin message)
        {
            _player = message.Player;

            // Thiết lập player context cho PacketHandlerActor
            if (_player != null && _actorNetState != null)
            {
                Logger.Instance.Debug($"Player ID {_player.AccountID} (SessionID: {_player.SessionID}) đã kết nối {_actorNetState.SessionID} ");
                // Cập nhật Client của Player để sử dụng ActorNetState
                PlayerNetworkManager.UpdatePlayerClient(_player, _actorNetState);

                // Gửi player context đến PacketHandlerActor
                _packetHandlerActor.Tell(new SetPlayerContext(_player, _actorNetState, _connection));

                Logger.Instance.Debug($"Đã thiết lập player context cho PacketHandlerActor cho người chơi {_player.CharacterName}");

                // Bây giờ xử lý đăng nhập sau khi Client đã được thiết lập
                if (!World.allConnectedChars.TryGetValue(message.Session.SessionId, out var _))
                {
                    _player.KetNoi_DangNhap(message.LoginData, message.LoginData.Length);

                    // Tích hợp AOI system cho player login
                    try
                    {
                        RxjhServer.Neo.AOIIntegration.OnPlayerLogin(_player);
                    }
                    catch (Exception ex)
                    {
                        Logger.Instance.Error($"Lỗi tích hợp AOI cho player {message.Username}: {ex.Message}");
                    }

                    Logger.Instance.Debug($"Người dùng {message.Username} đã đăng nhập thành công");
                    message.Session.IsAuthenticated = true;
                }
                else
                {
                    // Player da dang nhap roi
                    Logger.Instance.Debug($"Người dùng {message.Username} da co trong World");
                    // Xu ly ngat ket noi hoac khong cho dang nhap
                }
            }
        }

        private void HandleReceived(Tcp.Received received)
        {
            try
            {
                // Cập nhật thời gian hoạt động
                _session.UpdateActivity();

                // Chuyển đổi ByteString thành byte array
                byte[] data = received.Data.ToArray();
                byte[] decryptedData = Utils.Crypto.DecryptPacket(data);

                // Ghi log gói tin nhận được nếu cần
                //PacketLogger.LogIncomingPacket(_session.SessionId, data);

                // Gửi packet đến PacketHandlerActor thống nhất
                if (_player != null)
                {
                    // Nếu đã có player, gửi ProcessPlayerPacket
                   // Logger.Instance.Debug($"Chuyển tiếp packet đến PacketHandlerActor (ProcessPlayerPacket) cho session {_session.SessionId}");

                    _packetHandlerActor.Tell(new ProcessPlayerPacket(decryptedData, decryptedData.Length));
                }
                else
                {
                    // Nếu chưa có player, gửi ProcessPacket thông thường
                   // Logger.Instance.Debug($"Chuyển tiếp packet đến PacketHandlerActor (ProcessPacket) cho session {_session.SessionId}");

                    _packetHandlerActor.Tell(new ProcessPacket(_connection, _session, decryptedData));
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi xử lý dữ liệu nhận được từ client {_session.SessionId}: {ex.Message}");
            }
        }

        private void HandleConnectionClosed(Tcp.ConnectionClosed message)
        {
            // Kết nối đã đóng, dừng actor
            Context.Stop(Self);
        }
    }

    /// <summary>
    /// Message để thiết lập tham chiếu đến đối tượng Player
    /// </summary>
    public class SetPlayerReference
    {
        public Players Player { get; }

        public SetPlayerReference(Players player)
        {
            Player = player;
        }
    }

    /// <summary>
    /// Message để thiết lập tham chiếu đến đối tượng Player và xử lý đăng nhập
    /// </summary>
    public class SetPlayerReferenceAndLogin
    {
        public Players Player { get; }
        public byte[] LoginData { get; }
        public ClientSession Session { get; }
        public string Username { get; }

        public SetPlayerReferenceAndLogin(Players player, byte[] loginData, ClientSession session, string username)
        {
            Player = player;
            LoginData = loginData;
            Session = session;
            Username = username;
        }
    }

    /// <summary>
    /// Message yêu cầu xử lý gói tin
    /// </summary>
    public class ProcessPacket
    {
        public IActorRef Connection { get; }
        public ClientSession Session { get; }
        public byte[] Data { get; }

        public ProcessPacket(IActorRef connection, ClientSession session, byte[] data)
        {
            Connection = connection;
            Session = session;
            Data = data;
        }
    }
}
