using System;
using System.Collections.Generic;
using System.Collections.Concurrent;
using System.Linq;
using HeroYulgang.Helpers;

namespace RxjhServer.Neo
{
    /// <summary>
    /// Qu<PERSON>n lý hệ thống Area of Interest (AOI) cho game server
    /// Sử dụng grid system để tối ưu hóa việc tìm kiếm entities trong phạm vi
    /// </summary>
    public class AOIManager
    {
        private static AOIManager _instance;
        private static readonly object _lock = new object();

        // Cấu hình grid
        public const int GRID_SIZE = 512;           // Kích thước mỗi grid (512x512)
        public const int VISIBILITY_RANGE = 400;   // Phạm vi nhìn thấy
        public const int OVERLAP_SIZE = 200;       // Overlap giữa các grid để tránh miss entities
        public const int GRIDS_PER_MAP = 10;       // 10x10 grids cho map 5120x5120
        public const int MAP_SIZE = 5120;          // <PERSON><PERSON><PERSON> thước map
        public const int MAP_OFFSET = 2560;        // Offset từ tâm map (từ -2560 đến +2560)

        // Lưu trữ grids cho mỗi map
        private readonly ConcurrentDictionary<int, AOIGrid[,]> _mapGrids = new();

        // Tracking vị trí hiện tại của entities
        private readonly ConcurrentDictionary<int, (int mapId, int gridX, int gridY)> _playerGridPositions = new();
        private readonly ConcurrentDictionary<int, (int mapId, int gridX, int gridY)> _npcGridPositions = new();
        private readonly ConcurrentDictionary<long, (int mapId, int gridX, int gridY)> _itemGridPositions = new();

        public static AOIManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                        {
                            _instance = new AOIManager();
                        }
                    }
                }
                return _instance;
            }
        }

        private AOIManager()
        {
            LogHelper.WriteLine(LogLevel.Info, "AOIManager initialized");
        }

        #region Grid Management

        /// <summary>
        /// Khởi tạo grids cho một map
        /// </summary>
        public void InitializeMapGrids(int mapId)
        {
            if (_mapGrids.ContainsKey(mapId))
                return;

            try
            {
                var grids = new AOIGrid[GRIDS_PER_MAP, GRIDS_PER_MAP];

                for (int x = 0; x < GRIDS_PER_MAP; x++)
                {
                    for (int y = 0; y < GRIDS_PER_MAP; y++)
                    {
                        // Tính boundaries cho grid này
                        float minX = -MAP_OFFSET + (x * GRID_SIZE);
                        float maxX = minX + GRID_SIZE;
                        float minY = -MAP_OFFSET + (y * GRID_SIZE);
                        float maxY = minY + GRID_SIZE;

                        grids[x, y] = new AOIGrid(x, y, mapId, minX, maxX, minY, maxY);
                    }
                }

                _mapGrids.TryAdd(mapId, grids);
                LogHelper.WriteLine(LogLevel.Info, $"Initialized AOI grids for map {mapId}");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Failed to initialize grids for map {mapId}: {ex.Message}");
            }
        }

        /// <summary>
        /// Tính toán grid position từ tọa độ world
        /// </summary>
        public (int gridX, int gridY) GetGridPosition(float worldX, float worldY)
        {
            // Chuyển từ world coordinates (-2560 to +2560) sang grid coordinates (0 to 9)
            int gridX = (int)Math.Floor((worldX + MAP_OFFSET) / GRID_SIZE);
            int gridY = (int)Math.Floor((worldY + MAP_OFFSET) / GRID_SIZE);

            // Clamp để đảm bảo trong phạm vi hợp lệ
            gridX = Math.Max(0, Math.Min(GRIDS_PER_MAP - 1, gridX));
            gridY = Math.Max(0, Math.Min(GRIDS_PER_MAP - 1, gridY));

            return (gridX, gridY);
        }

        /// <summary>
        /// Lấy grid tại vị trí cụ thể
        /// </summary>
        public AOIGrid GetGrid(int mapId, int gridX, int gridY)
        {
            if (!_mapGrids.TryGetValue(mapId, out var grids))
            {
                InitializeMapGrids(mapId);
                if (!_mapGrids.TryGetValue(mapId, out grids))
                    return null;
            }

            if (gridX < 0 || gridX >= GRIDS_PER_MAP || gridY < 0 || gridY >= GRIDS_PER_MAP)
                return null;

            return grids[gridX, gridY];
        }

        /// <summary>
        /// Lấy tất cả grids lân cận (bao gồm overlap) cho một vị trí
        /// </summary>
        public List<AOIGrid> GetNearbyGrids(int mapId, float worldX, float worldY)
        {
            var result = new List<AOIGrid>();
            var (centerGridX, centerGridY) = GetGridPosition(worldX, worldY);

            // Kiểm tra các grid lân cận (3x3 area)
            for (int dx = -1; dx <= 1; dx++)
            {
                for (int dy = -1; dy <= 1; dy++)
                {
                    int gridX = centerGridX + dx;
                    int gridY = centerGridY + dy;

                    var grid = GetGrid(mapId, gridX, gridY);
                    if (grid != null)
                    {
                        result.Add(grid);
                    }
                }
            }

            return result;
        }

        #endregion

        #region Entity Management

        /// <summary>
        /// Thêm player vào AOI system
        /// </summary>
        public void AddPlayer(Players player)
        {
            if (player == null) return;

            try
            {
                var (gridX, gridY) = GetGridPosition(player.NhanVatToaDo_X, player.NhanVatToaDo_Y);
                var grid = GetGrid(player.NhanVatToaDo_BanDo, gridX, gridY);

                if (grid != null && grid.AddPlayer(player))
                {
                    _playerGridPositions.TryAdd(player.SessionID, (player.NhanVatToaDo_BanDo, gridX, gridY));
                    LogHelper.WriteLine(LogLevel.Debug, $"Added player {player.CharacterName} to grid [{gridX},{gridY}] on map {player.NhanVatToaDo_BanDo}");
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"AOIManager.AddPlayer error: {ex.Message}");
            }
        }

        /// <summary>
        /// Xóa player khỏi AOI system
        /// </summary>
        public void RemovePlayer(int sessionId)
        {
            try
            {
                if (_playerGridPositions.TryRemove(sessionId, out var position))
                {
                    var grid = GetGrid(position.mapId, position.gridX, position.gridY);
                    grid?.RemovePlayer(sessionId);
                    LogHelper.WriteLine(LogLevel.Debug, $"Removed player {sessionId} from grid [{position.gridX},{position.gridY}] on map {position.mapId}");
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"AOIManager.RemovePlayer error: {ex.Message}");
            }
        }

        /// <summary>
        /// Thêm NPC vào AOI system
        /// </summary>
        public void AddNPC(NpcClass npc)
        {
            if (npc == null) return;

            try
            {
                var (gridX, gridY) = GetGridPosition(npc.Rxjh_X, npc.Rxjh_Y);
                var grid = GetGrid(npc.Rxjh_Map, gridX, gridY);

                if (grid != null && grid.AddNPC(npc))
                {
                    _npcGridPositions.TryAdd(npc.NPC_SessionID, (npc.Rxjh_Map, gridX, gridY));
                    LogHelper.WriteLine(LogLevel.Debug, $"Added NPC {npc.NPC_SessionID} to grid [{gridX},{gridY}] on map {npc.Rxjh_Map}");
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"AOIManager.AddNPC error: {ex.Message}");
            }
        }

        /// <summary>
        /// Xóa NPC khỏi AOI system
        /// </summary>
        public void RemoveNPC(int npcSessionId)
        {
            try
            {
                if (_npcGridPositions.TryRemove(npcSessionId, out var position))
                {
                    var grid = GetGrid(position.mapId, position.gridX, position.gridY);
                    grid?.RemoveNPC(npcSessionId);
                    LogHelper.WriteLine(LogLevel.Debug, $"Removed NPC {npcSessionId} from grid [{position.gridX},{position.gridY}] on map {position.mapId}");
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"AOIManager.RemoveNPC error: {ex.Message}");
            }
        }

        /// <summary>
        /// Thêm item vào AOI system
        /// </summary>
        public void AddItem(X_Mat_Dat_Vat_Pham_Loai item)
        {
            if (item == null) return;

            try
            {
                var (gridX, gridY) = GetGridPosition(item.Rxjh_X, item.Rxjh_Y);
                var grid = GetGrid(item.Rxjh_Map, gridX, gridY);

                if (grid != null && grid.AddItem(item))
                {
                    _itemGridPositions.TryAdd(item.id, (item.Rxjh_Map, gridX, gridY));
                    LogHelper.WriteLine(LogLevel.Debug, $"Added item {item.id} to grid [{gridX},{gridY}] on map {item.Rxjh_Map}");
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"AOIManager.AddItem error: {ex.Message}");
            }
        }

        /// <summary>
        /// Xóa item khỏi AOI system
        /// </summary>
        public void RemoveItem(long itemId)
        {
            try
            {
                if (_itemGridPositions.TryRemove(itemId, out var position))
                {
                    var grid = GetGrid(position.mapId, position.gridX, position.gridY);
                    grid?.RemoveItem(itemId);
                    LogHelper.WriteLine(LogLevel.Debug, $"Removed item {itemId} from grid [{position.gridX},{position.gridY}] on map {position.mapId}");
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"AOIManager.RemoveItem error: {ex.Message}");
            }
        }

        #endregion

        #region Player Movement and Updates

        /// <summary>
        /// Cập nhật vị trí player và trả về các thay đổi AOI
        /// </summary>
        public AOIUpdateResult UpdatePlayerPosition(Players player)
        {
            var result = new AOIUpdateResult();

            if (player == null)
                return result;

            try
            {
                var newGridPos = GetGridPosition(player.NhanVatToaDo_X, player.NhanVatToaDo_Y);
                var currentMapId = player.NhanVatToaDo_BanDo;

                // Kiểm tra xem player có thay đổi grid không
                bool gridChanged = false;
                if (_playerGridPositions.TryGetValue(player.SessionID, out var oldPosition))
                {
                    gridChanged = oldPosition.mapId != currentMapId ||
                                 oldPosition.gridX != newGridPos.gridX ||
                                 oldPosition.gridY != newGridPos.gridY;

                    if (gridChanged)
                    {
                        // Xóa khỏi grid cũ
                        var oldGrid = GetGrid(oldPosition.mapId, oldPosition.gridX, oldPosition.gridY);
                        oldGrid?.RemovePlayer(player.SessionID);
                    }
                }
                else
                {
                    gridChanged = true; // Player mới
                }

                if (gridChanged)
                {
                    // Thêm vào grid mới
                    var newGrid = GetGrid(currentMapId, newGridPos.gridX, newGridPos.gridY);
                    if (newGrid != null)
                    {
                        newGrid.AddPlayer(player);
                        _playerGridPositions.AddOrUpdate(player.SessionID,
                            (currentMapId, newGridPos.gridX, newGridPos.gridY),
                            (key, oldVal) => (currentMapId, newGridPos.gridX, newGridPos.gridY));
                    }

                    // Tính toán các thay đổi AOI
                    result = CalculateAOIChanges(player);
                }

                return result;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"AOIManager.UpdatePlayerPosition error: {ex.Message}");
                return result;
            }
        }

        /// <summary>
        /// Tính toán các thay đổi AOI cho player
        /// </summary>
        private AOIUpdateResult CalculateAOIChanges(Players player)
        {
            var result = new AOIUpdateResult();

            try
            {
                // Lấy tất cả entities trong phạm vi từ nearby grids
                var nearbyGrids = GetNearbyGrids(player.NhanVatToaDo_BanDo, player.NhanVatToaDo_X, player.NhanVatToaDo_Y);

                var currentPlayers = new HashSet<int>();
                var currentNPCs = new HashSet<int>();
                var currentItems = new HashSet<long>();

                // Thu thập tất cả entities trong phạm vi
                foreach (var grid in nearbyGrids)
                {
                    // Players
                    foreach (var otherPlayer in grid.GetPlayers())
                    {
                        if (otherPlayer.SessionID != player.SessionID &&
                            IsInRange(player, otherPlayer.NhanVatToaDo_X, otherPlayer.NhanVatToaDo_Y))
                        {
                            currentPlayers.Add(otherPlayer.SessionID);

                            // Kiểm tra xem có phải player mới không
                            if (!player.PlayList.ContainsKey((World.ServerID, otherPlayer.SessionID)))
                            {
                                result.PlayersToAdd.Add(otherPlayer);
                            }
                        }
                    }

                    // NPCs
                    foreach (var npc in grid.GetNPCs())
                    {
                        if (IsInRange(player, npc.Rxjh_X, npc.Rxjh_Y))
                        {
                            currentNPCs.Add(npc.NPC_SessionID);

                            // Kiểm tra xem có phải NPC mới không
                            if (!player.NpcList.ContainsKey(npc.NPC_SessionID))
                            {
                                result.NPCsToAdd.Add(npc);
                            }
                        }
                    }

                    // Items
                    foreach (var item in grid.GetItems())
                    {
                        if (IsInRange(player, item.Rxjh_X, item.Rxjh_Y))
                        {
                            currentItems.Add(item.id);

                            // Kiểm tra xem có phải item mới không
                            if (!player.ListOfGroundItems.ContainsKey(item.id))
                            {
                                result.ItemsToAdd.Add(item);
                            }
                        }
                    }
                }

                // Tìm entities cần xóa (không còn trong phạm vi)
                foreach (var kvp in player.PlayList.ToList())
                {
                    if (!currentPlayers.Contains(kvp.Key.Item2))
                    {
                        result.PlayersToRemove.Add(kvp.Value);
                    }
                }

                foreach (var kvp in player.NpcList.ToList())
                {
                    if (!currentNPCs.Contains(kvp.Key))
                    {
                        result.NPCsToRemove.Add(kvp.Value);
                    }
                }

                foreach (var kvp in player.ListOfGroundItems.ToList())
                {
                    if (!currentItems.Contains((long)kvp.Key))
                    {
                        result.ItemsToRemove.Add(kvp.Value);
                    }
                }

            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"AOIManager.CalculateAOIChanges error: {ex.Message}");
            }

            return result;
        }

        /// <summary>
        /// Kiểm tra khoảng cách có trong phạm vi nhìn thấy không
        /// </summary>
        private bool IsInRange(Players player, float targetX, float targetY)
        {
            var deltaX = player.NhanVatToaDo_X - targetX;
            var deltaY = player.NhanVatToaDo_Y - targetY;
            var distance = Math.Sqrt(deltaX * deltaX + deltaY * deltaY);
            return distance <= VISIBILITY_RANGE;
        }

        #endregion

        #region Utility Methods

        /// <summary>
        /// Lấy thống kê về AOI system
        /// </summary>
        public string GetStatistics()
        {
            try
            {
                var totalPlayers = _playerGridPositions.Count;
                var totalNPCs = _npcGridPositions.Count;
                var totalItems = _itemGridPositions.Count;
                var totalMaps = _mapGrids.Count;

                return $"AOI Stats - Maps: {totalMaps}, Players: {totalPlayers}, NPCs: {totalNPCs}, Items: {totalItems}";
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"AOIManager.GetStatistics error: {ex.Message}");
                return "AOI Stats - Error retrieving statistics";
            }
        }

        /// <summary>
        /// Clear tất cả dữ liệu cho một map
        /// </summary>
        public void ClearMap(int mapId)
        {
            try
            {
                if (_mapGrids.TryRemove(mapId, out var grids))
                {
                    for (int x = 0; x < GRIDS_PER_MAP; x++)
                    {
                        for (int y = 0; y < GRIDS_PER_MAP; y++)
                        {
                            grids[x, y]?.Clear();
                        }
                    }
                }

                // Xóa tracking positions cho map này
                var playersToRemove = _playerGridPositions.Where(kvp => kvp.Value.mapId == mapId).Select(kvp => kvp.Key).ToList();
                var npcsToRemove = _npcGridPositions.Where(kvp => kvp.Value.mapId == mapId).Select(kvp => kvp.Key).ToList();
                var itemsToRemove = _itemGridPositions.Where(kvp => kvp.Value.mapId == mapId).Select(kvp => kvp.Key).ToList();

                foreach (var playerId in playersToRemove)
                    _playerGridPositions.TryRemove(playerId, out _);

                foreach (var npcId in npcsToRemove)
                    _npcGridPositions.TryRemove(npcId, out _);

                foreach (var itemId in itemsToRemove)
                    _itemGridPositions.TryRemove(itemId, out _);

                LogHelper.WriteLine(LogLevel.Info, $"Cleared AOI data for map {mapId}");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"AOIManager.ClearMap error: {ex.Message}");
            }
        }

        #endregion
    }
}
