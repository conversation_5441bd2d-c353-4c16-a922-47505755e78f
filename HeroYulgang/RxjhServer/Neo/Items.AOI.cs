using System;
using System.Collections.Generic;
using System.Linq;
using HeroYulgang.Helpers;
using RxjhServer.Neo;

namespace RxjhServer
{
    /// <summary>
    /// Extension methods cho Items để tích hợp AOI system
    /// </summary>
    public static class ItemsAOIExtensions
    {
        /// <summary>
        /// Thêm item vào AOI system
        /// </summary>
        public static void InitializeAOI(this X_Mat_Dat_Vat_Pham_Loai item)
        {
            try
            {
                if (item == null) return;
                
                AOIManager.Instance.AddItem(item);
                LogHelper.WriteLine(LogLevel.Debug, 
                    $"Item {item.id} initialized in AOI system at [{item.Rxjh_X},{item.Rxjh_Y}] on map {item.Rxjh_Map}");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, 
                    $"InitializeAOI error for item {item?.id}: {ex.Message}");
            }
        }

        /// <summary>
        /// Xóa item khỏi AOI system
        /// </summary>
        public static void CleanupAOI(this X_Mat_Dat_Vat_Pham_Loai item)
        {
            try
            {
                if (item == null) return;
                
                AOIManager.Instance.RemoveItem(item.id);
                LogHelper.WriteLine(LogLevel.Debug, 
                    $"Item {item.id} removed from AOI system");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, 
                    $"CleanupAOI error for item {item?.id}: {ex.Message}");
            }
        }

        /// <summary>
        /// Lấy tất cả players trong phạm vi của item sử dụng AOI
        /// </summary>
        public static List<Players> GetNearbyPlayersAOI(this X_Mat_Dat_Vat_Pham_Loai item, int range = -1)
        {
            var result = new List<Players>();
            
            try
            {
                if (item == null) return result;
                
                if (range == -1)
                    range = AOIManager.VISIBILITY_RANGE;

                // Lấy tất cả grids lân cận
                var nearbyGrids = AOIManager.Instance.GetNearbyGrids(item.Rxjh_Map, item.Rxjh_X, item.Rxjh_Y);
                
                foreach (var grid in nearbyGrids)
                {
                    foreach (var player in grid.GetPlayers())
                    {
                        if (IsPlayerInRange(item, player, range))
                        {
                            result.Add(player);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, 
                    $"GetNearbyPlayersAOI error for item {item?.id}: {ex.Message}");
            }
            
            return result;
        }

        /// <summary>
        /// Kiểm tra player có trong phạm vi của item không
        /// </summary>
        private static bool IsPlayerInRange(X_Mat_Dat_Vat_Pham_Loai item, Players player, int range)
        {
            if (item.Rxjh_Map != player.NhanVatToaDo_BanDo)
                return false;
                
            var deltaX = item.Rxjh_X - player.NhanVatToaDo_X;
            var deltaY = item.Rxjh_Y - player.NhanVatToaDo_Y;
            var distance = Math.Sqrt(deltaX * deltaX + deltaY * deltaY);
            return distance <= range;
        }

        /// <summary>
        /// Gửi thông báo item spawn tới tất cả players trong phạm vi sử dụng AOI
        /// </summary>
        public static void NotifySpawnAOI(this X_Mat_Dat_Vat_Pham_Loai item)
        {
            try
            {
                if (item == null) return;

                var nearbyPlayers = item.GetNearbyPlayersAOI();
                
                foreach (var player in nearbyPlayers)
                {
                    if (player.Client != null && !player.Client.TreoMay)
                    {
                        // Thêm item vào danh sách của player
                        if (!player.ListOfGroundItems.ContainsKey(item.id))
                        {
                            player.ListOfGroundItems.Add(item.id, item);
                            
                            if (item.PlayList != null && !item.PlayList.ContainsKey(player.SessionID))
                            {
                                item.PlayList.Add(player.SessionID, player);
                            }
                            
                            // Gửi packet spawn item
                            var itemDict = new Dictionary<long, X_Mat_Dat_Vat_Pham_Loai> { { item.id, item } };
                            player.IncreaseInGroundItems(itemDict);
                        }
                    }
                }
                
                LogHelper.WriteLine(LogLevel.Debug, 
                    $"Item {item.id} spawn notification sent to {nearbyPlayers.Count} players via AOI");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, 
                    $"NotifySpawnAOI error for item {item?.id}: {ex.Message}");
            }
        }

        /// <summary>
        /// Gửi thông báo item despawn tới tất cả players trong phạm vi sử dụng AOI
        /// </summary>
        public static void NotifyDespawnAOI(this X_Mat_Dat_Vat_Pham_Loai item)
        {
            try
            {
                if (item == null) return;

                var nearbyPlayers = item.GetNearbyPlayersAOI();
                
                foreach (var player in nearbyPlayers)
                {
                    if (player.Client != null && !player.Client.TreoMay)
                    {
                        // Xóa item khỏi danh sách của player
                        if (player.ListOfGroundItems.ContainsKey(item.id))
                        {
                            player.ListOfGroundItems.Remove(item.id);
                            
                            if (item.PlayList != null && item.PlayList.ContainsKey(player.SessionID))
                            {
                                item.PlayList.Remove(player.SessionID);
                            }
                            
                            // Gửi packet despawn item
                            player.VatPham_Drop_BienMat(item.id);
                        }
                    }
                }
                
                LogHelper.WriteLine(LogLevel.Debug, 
                    $"Item {item.id} despawn notification sent to {nearbyPlayers.Count} players via AOI");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, 
                    $"NotifyDespawnAOI error for item {item?.id}: {ex.Message}");
            }
        }
    }

    /// <summary>
    /// Utility class để quản lý items trong AOI system
    /// </summary>
    public static class AOIItemManager
    {
        /// <summary>
        /// Thêm item mới vào world và AOI system
        /// </summary>
        public static void AddItemToWorld(X_Mat_Dat_Vat_Pham_Loai item)
        {
            try
            {
                if (item == null) return;

                // Thêm vào World.ItmeTeM
                if (!World.ItmeTeM.ContainsKey(item.id))
                {
                    World.ItmeTeM.Add(item.id, item);
                }

                // Thêm vào AOI system
                item.InitializeAOI();

                // Thông báo spawn tới players lân cận
                item.NotifySpawnAOI();

                LogHelper.WriteLine(LogLevel.Debug, 
                    $"Item {item.id} added to world and AOI system");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, 
                    $"AddItemToWorld error for item {item?.id}: {ex.Message}");
            }
        }

        /// <summary>
        /// Xóa item khỏi world và AOI system
        /// </summary>
        public static void RemoveItemFromWorld(long itemId)
        {
            try
            {
                if (World.ItmeTeM.TryGetValue(itemId, out var item))
                {
                    // Thông báo despawn tới players lân cận
                    item.NotifyDespawnAOI();

                    // Xóa khỏi AOI system
                    item.CleanupAOI();

                    // Xóa khỏi World.ItmeTeM
                    World.ItmeTeM.Remove(itemId);

                    LogHelper.WriteLine(LogLevel.Debug, 
                        $"Item {itemId} removed from world and AOI system");
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, 
                    $"RemoveItemFromWorld error for item {itemId}: {ex.Message}");
            }
        }

        /// <summary>
        /// Cleanup tất cả items cho một map
        /// </summary>
        public static void CleanupMapItems(int mapId)
        {
            try
            {
                var itemsToRemove = World.ItmeTeM.Values
                    .Where(item => item.Rxjh_Map == mapId)
                    .Select(item => item.id)
                    .ToList();

                foreach (var itemId in itemsToRemove)
                {
                    RemoveItemFromWorld(itemId);
                }

                LogHelper.WriteLine(LogLevel.Info, 
                    $"Cleaned up {itemsToRemove.Count} items for map {mapId}");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, 
                    $"CleanupMapItems error for map {mapId}: {ex.Message}");
            }
        }

        /// <summary>
        /// Lấy thống kê items trong AOI system
        /// </summary>
        public static string GetItemStatistics()
        {
            try
            {
                var totalItems = World.ItmeTeM.Count;
                var mapGroups = World.ItmeTeM.Values.GroupBy(item => item.Rxjh_Map);
                var mapStats = string.Join(", ", mapGroups.Select(g => $"Map {g.Key}: {g.Count()}"));
                
                return $"Total Items: {totalItems} | Per Map: {mapStats}";
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"GetItemStatistics error: {ex.Message}");
                return "Item Statistics - Error retrieving data";
            }
        }
    }
}
