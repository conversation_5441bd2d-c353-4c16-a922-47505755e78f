using System.Collections.Generic;

namespace RxjhServer.Neo
{
    /// <summary>
    /// Kết quả cập nhật AOI cho một player
    /// Ch<PERSON><PERSON> danh sách các entities cần thêm/xóa khỏi tầm nhìn
    /// </summary>
    public class AOIUpdateResult
    {
        /// <summary>
        /// Danh sách players mới cần thêm vào tầm nhìn
        /// </summary>
        public List<Players> PlayersToAdd { get; set; } = new();
        
        /// <summary>
        /// Danh sách players cần xóa khỏi tầm nhìn
        /// </summary>
        public List<Players> PlayersToRemove { get; set; } = new();
        
        /// <summary>
        /// Danh sách NPCs mới cần thêm vào tầm nhìn
        /// </summary>
        public List<NpcClass> NPCsToAdd { get; set; } = new();
        
        /// <summary>
        /// Danh sách NPCs cần xóa khỏi tầm nhìn
        /// </summary>
        public List<NpcClass> NPCsToRemove { get; set; } = new();
        
        /// <summary>
        /// Danh sách items mới cần thêm vào tầm nhìn
        /// </summary>
        public List<X_Mat_Dat_Vat_Pham_Loai> ItemsToAdd { get; set; } = new();
        
        /// <summary>
        /// Danh sách items cần xóa khỏi tầm nhìn
        /// </summary>
        public List<X_Mat_Dat_Vat_Pham_Loai> ItemsToRemove { get; set; } = new();
        
        /// <summary>
        /// Kiểm tra có thay đổi nào không
        /// </summary>
        public bool HasChanges => 
            PlayersToAdd.Count > 0 || PlayersToRemove.Count > 0 ||
            NPCsToAdd.Count > 0 || NPCsToRemove.Count > 0 ||
            ItemsToAdd.Count > 0 || ItemsToRemove.Count > 0;
        
        /// <summary>
        /// Reset tất cả danh sách
        /// </summary>
        public void Clear()
        {
            PlayersToAdd.Clear();
            PlayersToRemove.Clear();
            NPCsToAdd.Clear();
            NPCsToRemove.Clear();
            ItemsToAdd.Clear();
            ItemsToRemove.Clear();
        }
        
        /// <summary>
        /// Lấy thông tin tóm tắt về các thay đổi
        /// </summary>
        public string GetSummary()
        {
            return $"Players: +{PlayersToAdd.Count}/-{PlayersToRemove.Count}, " +
                   $"NPCs: +{NPCsToAdd.Count}/-{NPCsToRemove.Count}, " +
                   $"Items: +{ItemsToAdd.Count}/-{ItemsToRemove.Count}";
        }
    }
}
