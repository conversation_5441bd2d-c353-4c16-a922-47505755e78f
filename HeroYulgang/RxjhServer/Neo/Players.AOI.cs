using System;
using System.Collections.Generic;
using System.Linq;
using HeroYulgang.Helpers;
using RxjhServer.Neo;

namespace RxjhServer
{
    /// <summary>
    /// Extension methods cho Players class để tích hợp AOI system
    /// </summary>
    public partial class Players
    {
        /// <summary>
        /// Cập nhật AOI sử dụng hệ thống grid mới
        /// Thay thế cho GetTheReviewRangePlayers() cũ
        /// </summary>
        public virtual void GetTheReviewRangePlayersAOI(bool skipBS = false)
        {
            try
            {
                if (CurrentZone != null && CurrentZone.IsCrossServer && !skipBS)
                {
                    GetTheReviewRangePlayersCrossServer();
                    return;
                }

                if (PlayList == null || Client.TreoMay)
                {
                    return;
                }

                // Sử dụng AOI system để lấy updates
                var aoiResult = AOIManager.Instance.UpdatePlayerPosition(this);
                
                if (!aoiResult.HasChanges)
                {
                    return; // Không có thay đổi nào
                }

                // Xử lý players cần thêm
                foreach (var playerToAdd in aoiResult.PlayersToAdd)
                {
                    if (!PlayList.ContainsKey((World.ServerID, playerToAdd.SessionID)) && 
                        NhanVatToaDo_BanDo == playerToAdd.NhanVatToaDo_BanDo)
                    {
                        PlayList.Add((World.ServerID, playerToAdd.SessionID), playerToAdd);
                        UpdateCharacterData(playerToAdd);
                        
                        // Thêm ngược lại
                        if (!playerToAdd.PlayList.ContainsKey((World.ServerID, SessionID)))
                        {
                            playerToAdd.PlayList.Add((World.ServerID, SessionID), this);
                            playerToAdd.UpdateCharacterData(this);
                        }
                        
                        // Xử lý GM mode và stealth
                        HandlePlayerVisibilityEffects(playerToAdd);
                        HandleDaiChienHon(playerToAdd);
                    }
                }

                // Xử lý players cần xóa
                foreach (var playerToRemove in aoiResult.PlayersToRemove)
                {
                    if (PlayList.ContainsKey((World.ServerID, playerToRemove.SessionID)))
                    {
                        PlayList.Remove((World.ServerID, playerToRemove.SessionID));
                        DiChuyen_RaKhoi_BanDo(this, playerToRemove);
                        
                        // Xóa ngược lại
                        if (playerToRemove.PlayList.ContainsKey((World.ServerID, SessionID)))
                        {
                            playerToRemove.PlayList.Remove((World.ServerID, SessionID));
                            DiChuyen_RaKhoi_BanDo(playerToRemove, this);
                        }
                    }
                }

                LogHelper.WriteLine(LogLevel.Debug, 
                    $"AOI Update for {CharacterName}: {aoiResult.GetSummary()}");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, 
                    $"GetTheReviewRangePlayersAOI error for player {CharacterName}: {ex.Message}");
            }
        }

        /// <summary>
        /// Cập nhật NPC AOI sử dụng hệ thống grid mới
        /// Thay thế cho GetReviewScopeNpc() cũ
        /// </summary>
        public virtual void GetReviewScopeNpcAOI(bool skipBS = false)
        {
            try
            {
                if (CurrentZone != null && CurrentZone.IsCrossServer && !skipBS)
                {
                    GetReviewScopeNpcCrossServer();
                    return;
                }

                if (NpcList == null || Client.TreoMay)
                {
                    return;
                }

                // Sử dụng AOI system để lấy updates
                var aoiResult = AOIManager.Instance.UpdatePlayerPosition(this);
                
                if (aoiResult.NPCsToAdd.Count == 0 && aoiResult.NPCsToRemove.Count == 0)
                {
                    return; // Không có thay đổi NPC nào
                }

                var npcsToAdd = new Dictionary<int, NpcClass>();
                var npcsToRemove = new Dictionary<int, NpcClass>();

                // Xử lý NPCs cần thêm
                foreach (var npcToAdd in aoiResult.NPCsToAdd)
                {
                    if (!NpcList.ContainsKey(npcToAdd.NPC_SessionID))
                    {
                        NpcList.Add(npcToAdd.NPC_SessionID, npcToAdd);
                        npcToAdd.PlayList_Add(this);
                        npcsToAdd.Add(npcToAdd.NPC_SessionID, npcToAdd);
                    }
                }

                // Xử lý NPCs cần xóa
                foreach (var npcToRemove in aoiResult.NPCsToRemove)
                {
                    if (NpcList.ContainsKey(npcToRemove.NPC_SessionID))
                    {
                        NpcList.Remove(npcToRemove.NPC_SessionID);
                        if (Client != null && npcToRemove.Contains(this))
                        {
                            npcToRemove.PlayList_Remove(this);
                        }
                        npcsToRemove.Add(npcToRemove.NPC_SessionID, npcToRemove);
                    }
                }

                // Gửi updates tới client
                if (npcsToRemove.Count > 0)
                {
                    NpcClass.UpdateNPC_DeXoaSoLieu(npcsToRemove, this);
                }
                
                if (npcsToAdd.Count > 0)
                {
                    NpcClass.UpdateNPCSoLieu(npcsToAdd, this);
                }

                LogHelper.WriteLine(LogLevel.Debug, 
                    $"NPC AOI Update for {CharacterName}: +{npcsToAdd.Count}/-{npcsToRemove.Count}");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, 
                    $"GetReviewScopeNpcAOI error for player {CharacterName}: {ex.Message}");
            }
        }

        /// <summary>
        /// Cập nhật Items AOI sử dụng hệ thống grid mới
        /// Thay thế cho ThuThap_VatPham_Drop_PhamVi() cũ
        /// </summary>
        public void ThuThap_VatPham_Drop_PhamViAOI()
        {
            if (ListOfGroundItems == null)
            {
                return;
            }

            try
            {
                // Sử dụng AOI system để lấy updates
                var aoiResult = AOIManager.Instance.UpdatePlayerPosition(this);
                
                if (aoiResult.ItemsToAdd.Count == 0 && aoiResult.ItemsToRemove.Count == 0)
                {
                    return; // Không có thay đổi items nào
                }

                var itemsToAdd = new Dictionary<long, X_Mat_Dat_Vat_Pham_Loai>();
                var itemsToRemove = new List<long>();

                // Xử lý items cần thêm
                foreach (var itemToAdd in aoiResult.ItemsToAdd)
                {
                    if (!ListOfGroundItems.ContainsKey(itemToAdd.id))
                    {
                        ListOfGroundItems.Add(itemToAdd.id, itemToAdd);
                        
                        if (itemToAdd.PlayList != null)
                        {
                            if (!itemToAdd.PlayList.ContainsKey(SessionID))
                            {
                                itemToAdd.PlayList.Add(SessionID, this);
                            }
                            itemsToAdd.Add(itemToAdd.id, itemToAdd);
                        }
                    }
                }

                // Xử lý items cần xóa
                foreach (var itemToRemove in aoiResult.ItemsToRemove)
                {
                    if (ListOfGroundItems.ContainsKey(itemToRemove.id))
                    {
                        ListOfGroundItems.Remove(itemToRemove.id);
                        itemsToRemove.Add(itemToRemove.id);
                    }
                }

                // Gửi remove packets cho items bị xóa
                foreach (var itemId in itemsToRemove)
                {
                    VatPham_Drop_BienMat(itemId);
                }

                // Gửi add packets cho items mới
                if (itemsToAdd.Count > 0)
                {
                    IncreaseInGroundItems(itemsToAdd);
                }

                LogHelper.WriteLine(LogLevel.Debug, 
                    $"Items AOI Update for {CharacterName}: +{itemsToAdd.Count}/-{itemsToRemove.Count}");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, 
                    $"ThuThap_VatPham_Drop_PhamViAOI error for player {CharacterName}: {ex.Message}");
            }
        }

        /// <summary>
        /// Xử lý hiệu ứng visibility cho players (GM mode, stealth, etc.)
        /// </summary>
        private void HandlePlayerVisibilityEffects(Players otherPlayer)
        {
            try
            {
                if (otherPlayer.GMMode != 0 && otherPlayer.HinhThuc_TangHinh != 0)
                {
                    otherPlayer.HinhThuc_TangHinh = 1;
                    otherPlayer.CheDo_TangHinh(1);
                }
                
                if (GMMode != 0 && HinhThuc_TangHinh != 0)
                {
                    HinhThuc_TangHinh = 1;
                    CheDo_TangHinh(1);
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, 
                    $"HandlePlayerVisibilityEffects error: {ex.Message}");
            }
        }

        /// <summary>
        /// Hook vào movement để cập nhật AOI
        /// </summary>
        public void OnPlayerMoveAOI(float newX, float newY, float newZ)
        {
            try
            {
                // Cập nhật tọa độ
                NhanVatToaDo_X = newX;
                NhanVatToaDo_Y = newY;
                NhanVatToaDo_Z = newZ;

                // Trigger AOI updates
                GetTheReviewRangePlayersAOI();
                GetReviewScopeNpcAOI();
                ThuThap_VatPham_Drop_PhamViAOI();

                // Hook cho Zone system nếu có
                Hooks_PlayerMove.AfterPlayerMove(this);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, 
                    $"OnPlayerMoveAOI error for player {CharacterName}: {ex.Message}");
            }
        }

        /// <summary>
        /// Khởi tạo player trong AOI system
        /// </summary>
        public void InitializeAOI()
        {
            try
            {
                AOIManager.Instance.AddPlayer(this);
                LogHelper.WriteLine(LogLevel.Debug, 
                    $"Player {CharacterName} initialized in AOI system");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, 
                    $"InitializeAOI error for player {CharacterName}: {ex.Message}");
            }
        }

        /// <summary>
        /// Cleanup player khỏi AOI system
        /// </summary>
        public void CleanupAOI()
        {
            try
            {
                AOIManager.Instance.RemovePlayer(SessionID);
                LogHelper.WriteLine(LogLevel.Debug, 
                    $"Player {CharacterName} removed from AOI system");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, 
                    $"CleanupAOI error for player {CharacterName}: {ex.Message}");
            }
        }
    }
}
