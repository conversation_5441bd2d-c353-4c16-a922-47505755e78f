using System;
using HeroYulgang.Helpers;
using RxjhServer.Neo;

namespace RxjhServer.Neo
{
    /// <summary>
    /// Hooks để tích hợp AOI system vào các hàm hiện có
    /// Cho phép chuyển đổi dần từ hệ thống cũ sang AOI mà không phá vỡ tính năng hiện có
    /// </summary>
    public static class AOIHooks
    {
        /// <summary>
        /// Cờ để bật/tắt AOI system
        /// </summary>
        public static bool UseAOISystem { get; set; } = true;

        /// <summary>
        /// C<PERSON> để log chi tiết AOI operations
        /// </summary>
        public static bool EnableAOILogging { get; set; } = false;

        #region Player Hooks

        /// <summary>
        /// Hook cho player login
        /// </summary>
        public static void OnPlayerLogin(Players player)
        {
            try
            {
                if (!UseAOISystem || player == null) return;

                World.AddPlayerToAOI(player);
                
                if (EnableAOILogging)
                {
                    LogHelper.WriteLine(LogLevel.Debug, 
                        $"AOI Hook: Player {player.CharacterName} logged in");
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, 
                    $"AOIHooks.OnPlayerLogin error: {ex.Message}");
            }
        }

        /// <summary>
        /// Hook cho player logout
        /// </summary>
        public static void OnPlayerLogout(Players player)
        {
            try
            {
                if (!UseAOISystem || player == null) return;

                World.RemovePlayerFromAOI(player);
                
                if (EnableAOILogging)
                {
                    LogHelper.WriteLine(LogLevel.Debug, 
                        $"AOI Hook: Player {player.CharacterName} logged out");
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, 
                    $"AOIHooks.OnPlayerLogout error: {ex.Message}");
            }
        }

        /// <summary>
        /// Hook cho player movement
        /// </summary>
        public static void OnPlayerMove(Players player, float newX, float newY, float newZ)
        {
            try
            {
                if (!UseAOISystem || player == null) return;

                // Sử dụng AOI system để cập nhật
                player.OnPlayerMoveAOI(newX, newY, newZ);
                
                if (EnableAOILogging)
                {
                    LogHelper.WriteLine(LogLevel.Debug, 
                        $"AOI Hook: Player {player.CharacterName} moved to [{newX},{newY}]");
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, 
                    $"AOIHooks.OnPlayerMove error: {ex.Message}");
            }
        }

        /// <summary>
        /// Hook cho player teleport/map change
        /// </summary>
        public static void OnPlayerMapChange(Players player, int oldMapId, int newMapId)
        {
            try
            {
                if (!UseAOISystem || player == null) return;

                // Xóa khỏi map cũ
                player.CleanupAOI();
                
                // Thêm vào map mới
                AOIManager.Instance.InitializeMapGrids(newMapId);
                player.InitializeAOI();
                
                if (EnableAOILogging)
                {
                    LogHelper.WriteLine(LogLevel.Debug, 
                        $"AOI Hook: Player {player.CharacterName} changed map from {oldMapId} to {newMapId}");
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, 
                    $"AOIHooks.OnPlayerMapChange error: {ex.Message}");
            }
        }

        #endregion

        #region NPC Hooks

        /// <summary>
        /// Hook cho NPC spawn
        /// </summary>
        public static void OnNPCSpawn(NpcClass npc)
        {
            try
            {
                if (!UseAOISystem || npc == null) return;

                World.AddNPCToAOI(npc);
                
                if (EnableAOILogging)
                {
                    LogHelper.WriteLine(LogLevel.Debug, 
                        $"AOI Hook: NPC {npc.NPC_SessionID} (PID: {npc.FLD_PID}) spawned");
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, 
                    $"AOIHooks.OnNPCSpawn error: {ex.Message}");
            }
        }

        /// <summary>
        /// Hook cho NPC despawn/death
        /// </summary>
        public static void OnNPCDespawn(NpcClass npc)
        {
            try
            {
                if (!UseAOISystem || npc == null) return;

                World.RemoveNPCFromAOI(npc);
                
                if (EnableAOILogging)
                {
                    LogHelper.WriteLine(LogLevel.Debug, 
                        $"AOI Hook: NPC {npc.NPC_SessionID} (PID: {npc.FLD_PID}) despawned");
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, 
                    $"AOIHooks.OnNPCDespawn error: {ex.Message}");
            }
        }

        /// <summary>
        /// Hook cho NPC movement
        /// </summary>
        public static void OnNPCMove(NpcClass npc, float newX, float newY, float newZ)
        {
            try
            {
                if (!UseAOISystem || npc == null) return;

                npc.UpdatePositionAOI(newX, newY, newZ);
                
                if (EnableAOILogging)
                {
                    LogHelper.WriteLine(LogLevel.Debug, 
                        $"AOI Hook: NPC {npc.NPC_SessionID} moved to [{newX},{newY}]");
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, 
                    $"AOIHooks.OnNPCMove error: {ex.Message}");
            }
        }

        #endregion

        #region Item Hooks

        /// <summary>
        /// Hook cho item drop
        /// </summary>
        public static void OnItemDrop(X_Mat_Dat_Vat_Pham_Loai item)
        {
            try
            {
                if (!UseAOISystem || item == null) return;

                World.AddItemToAOI(item);
                
                if (EnableAOILogging)
                {
                    LogHelper.WriteLine(LogLevel.Debug, 
                        $"AOI Hook: Item {item.id} dropped at [{item.Rxjh_X},{item.Rxjh_Y}]");
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, 
                    $"AOIHooks.OnItemDrop error: {ex.Message}");
            }
        }

        /// <summary>
        /// Hook cho item pickup/despawn
        /// </summary>
        public static void OnItemRemove(long itemId)
        {
            try
            {
                if (!UseAOISystem) return;

                World.RemoveItemFromAOI(itemId);
                
                if (EnableAOILogging)
                {
                    LogHelper.WriteLine(LogLevel.Debug, 
                        $"AOI Hook: Item {itemId} removed");
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, 
                    $"AOIHooks.OnItemRemove error: {ex.Message}");
            }
        }

        #endregion

        #region System Hooks

        /// <summary>
        /// Hook cho server startup
        /// </summary>
        public static void OnServerStartup()
        {
            try
            {
                if (!UseAOISystem) return;

                World.InitializeAOISystem();
                
                LogHelper.WriteLine(LogLevel.Info, 
                    "AOI Hook: Server startup completed, AOI system initialized");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, 
                    $"AOIHooks.OnServerStartup error: {ex.Message}");
            }
        }

        /// <summary>
        /// Hook cho map cleanup
        /// </summary>
        public static void OnMapCleanup(int mapId)
        {
            try
            {
                if (!UseAOISystem) return;

                World.CleanupMapAOI(mapId);
                
                if (EnableAOILogging)
                {
                    LogHelper.WriteLine(LogLevel.Debug, 
                        $"AOI Hook: Map {mapId} cleaned up");
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, 
                    $"AOIHooks.OnMapCleanup error: {ex.Message}");
            }
        }

        #endregion

        #region Utility Methods

        /// <summary>
        /// Bật/tắt AOI system
        /// </summary>
        public static void SetAOIEnabled(bool enabled)
        {
            UseAOISystem = enabled;
            LogHelper.WriteLine(LogLevel.Info, 
                $"AOI System {(enabled ? "enabled" : "disabled")}");
        }

        /// <summary>
        /// Bật/tắt AOI logging
        /// </summary>
        public static void SetAOILogging(bool enabled)
        {
            EnableAOILogging = enabled;
            LogHelper.WriteLine(LogLevel.Info, 
                $"AOI Logging {(enabled ? "enabled" : "disabled")}");
        }

        /// <summary>
        /// Lấy trạng thái AOI system
        /// </summary>
        public static string GetAOIStatus()
        {
            return $"AOI System: {(UseAOISystem ? "Enabled" : "Disabled")}, " +
                   $"Logging: {(EnableAOILogging ? "Enabled" : "Disabled")}";
        }

        /// <summary>
        /// Force refresh AOI cho tất cả players
        /// </summary>
        public static void RefreshAllPlayersAOI()
        {
            try
            {
                if (!UseAOISystem) return;

                int refreshedCount = 0;
                foreach (var player in World.allConnectedChars.Values)
                {
                    try
                    {
                        player.GetTheReviewRangePlayersAOI();
                        player.GetReviewScopeNpcAOI();
                        player.ThuThap_VatPham_Drop_PhamViAOI();
                        refreshedCount++;
                    }
                    catch (Exception ex)
                    {
                        LogHelper.WriteLine(LogLevel.Warning, 
                            $"Failed to refresh AOI for player {player.CharacterName}: {ex.Message}");
                    }
                }

                LogHelper.WriteLine(LogLevel.Info, 
                    $"AOI refreshed for {refreshedCount} players");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, 
                    $"RefreshAllPlayersAOI error: {ex.Message}");
            }
        }

        #endregion
    }
}
