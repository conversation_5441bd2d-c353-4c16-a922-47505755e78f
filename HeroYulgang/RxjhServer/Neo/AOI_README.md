# Hệ thống Area of Interest (AOI) cho Game Server

## Tổng quan

Hệ thống AOI mới được thiết kế để tối ưu hóa hiệu suất game server bằng cách sử dụng grid system thay vì duyệt qua tất cả entities mỗi lần cập nhật. Hệ thống này giải quyết các vấn đề:

- **Performance**: Gi<PERSON><PERSON> độ phức tạp từ O(n²) xuống O(n)
- **Grid boundary**: Sử dụng overlap system để players ở gần nhau không bị miss
- **Incremental updates**: Chỉ gửi thay đổi thực sự, không gửi lại toàn bộ

## Cấu trúc hệ thống

### 1. AOIManager (Singleton)
- Quản lý toàn bộ grid system
- Chia mỗi map 5120x5120 thành 10x10 grids (512x512 mỗi grid)
- Overlap 200 units giữa các grids để tránh miss entities
- Thread-safe operations

### 2. AOIGrid
- Lưu trữ entities trong mỗi grid
- Thread-safe collections cho players, NPCs, items
- Hỗ trợ add/remove/query operations

### 3. AOIUpdateResult
- Chứa kết quả cập nhật AOI
- Danh sách entities cần thêm/xóa
- Hỗ trợ incremental updates

## Cách sử dụng

### Khởi tạo hệ thống

```csharp
// Trong server startup
AOIIntegration.InitializeOnServerStartup();
```

### Player operations

```csharp
// Player login
AOIIntegration.OnPlayerLogin(player);

// Player movement
AOIIntegration.OnPlayerMove(player, newX, newY, newZ);

// Player map change
AOIIntegration.OnPlayerMapChange(player, oldMapId, newMapId);

// Player logout
AOIIntegration.OnPlayerLogout(player);
```

### NPC operations

```csharp
// NPC spawn
AOIIntegration.OnNPCSpawn(npc);

// NPC movement
npc.UpdatePositionAOI(newX, newY, newZ);

// NPC despawn
AOIIntegration.OnNPCDespawn(npc);
```

### Item operations

```csharp
// Item drop
AOIIntegration.OnItemDrop(item);

// Item pickup/remove
AOIIntegration.OnItemRemove(itemId);
```

## Cấu hình

### Constants trong AOIManager

```csharp
public const int GRID_SIZE = 512;           // Kích thước mỗi grid
public const int VISIBILITY_RANGE = 400;   // Phạm vi nhìn thấy
public const int OVERLAP_SIZE = 200;       // Overlap giữa grids
public const int GRIDS_PER_MAP = 10;       // 10x10 grids
```

### Bật/tắt hệ thống

```csharp
// Bật AOI system
AOIIntegration.SetAOIEnabled(true);

// Tắt AOI system (fallback to original)
AOIIntegration.SetAOIEnabled(false);

// Bật/tắt logging
AOIHooks.SetAOILogging(true);
```

## Tích hợp vào code hiện có

Hệ thống được thiết kế để tương thích ngược. Các hàm hiện có sẽ tự động sử dụng AOI nếu được bật:

```csharp
// Các hàm này tự động sử dụng AOI nếu enabled
player.GetTheReviewRangePlayers();
player.GetReviewScopeNpc();
player.ThuThap_VatPham_Drop_PhamVi();
```

## Monitoring và Debugging

### Lấy thống kê

```csharp
// Thống kê tổng quan
string stats = AOIIntegration.GetAOIStatistics();

// Status hệ thống
string status = AOIIntegration.GetAOIStatus();
```

### Maintenance

```csharp
// Periodic maintenance (gọi từ timer)
AOIIntegration.PeriodicMaintenance();

// Validate và repair
World.ValidateAndRepairAOI();

// Refresh tất cả players
AOIHooks.RefreshAllPlayersAOI();
```

## Performance Benefits

### Trước khi có AOI
- Duyệt qua TẤT CẢ players/NPCs mỗi lần update
- Độ phức tạp: O(n²) với n là số entities
- Với 1000 players: 1,000,000 operations

### Sau khi có AOI
- Chỉ kiểm tra entities trong nearby grids (3x3 = 9 grids)
- Độ phức tạp: O(n) với n là số entities trong nearby grids
- Với 1000 players: ~90 operations (giả sử phân bố đều)

## Troubleshooting

### Vấn đề thường gặp

1. **Players không nhìn thấy nhau**
   - Kiểm tra AOI system có được bật không
   - Kiểm tra grid boundaries
   - Verify overlap settings

2. **Performance vẫn chậm**
   - Kiểm tra distribution của entities
   - Có thể cần điều chỉnh GRID_SIZE
   - Monitor memory usage

3. **Entities bị miss**
   - Tăng OVERLAP_SIZE
   - Kiểm tra edge cases ở grid boundaries
   - Verify distance calculations

### Debug commands

```csharp
// Kiểm tra player position trong grid
var (gridX, gridY) = AOIManager.Instance.GetGridPosition(player.NhanVatToaDo_X, player.NhanVatToaDo_Y);

// Lấy nearby grids
var nearbyGrids = AOIManager.Instance.GetNearbyGrids(mapId, x, y);

// Force refresh một player
AOIIntegration.RefreshPlayerAOI(player);
```

## Migration từ hệ thống cũ

1. **Backup code hiện tại**
2. **Deploy AOI system** với `UseAOISystem = false`
3. **Test thoroughly** với AOI disabled
4. **Enable AOI** từ từ: `AOIIntegration.SetAOIEnabled(true)`
5. **Monitor performance** và adjust nếu cần
6. **Remove old code** sau khi stable

## ✅ Tích hợp hoàn tất

AOI System đã được tích hợp đầy đủ vào server:

### Server Startup Integration
- ✅ Tự động khởi tạo khi server start (MainWindow.axaml.cs)
- ✅ Chạy basic tests để verify functionality
- ✅ Error handling và logging

### Player Lifecycle Integration
- ✅ Player login: Tự động setup AOI (ClientActor.cs)
- ✅ Player logout: Tự động cleanup AOI (TcpManagerActor.cs, ActorNetState.cs)
- ✅ Session management integration

### Movement Integration
- ✅ Tích hợp vào existing movement hooks (Hooks_PlayerMove.cs)
- ✅ Cross-server movement support (Players.CrossServerZone.cs)
- ✅ Tự động trigger AOI updates khi player di chuyển
- ✅ Backward compatibility với existing code

### Testing và Monitoring
- ✅ AOITest.cs với comprehensive test suite
- ✅ Performance testing và stress testing
- ✅ Statistics và monitoring capabilities
- ✅ Debug commands và troubleshooting tools

### Files được tạo/sửa đổi:
1. **AOIManager.cs** - Core AOI management
2. **AOIGrid.cs** - Grid system implementation
3. **AOIUpdateResult.cs** - Update result structure
4. **AOIHooks.cs** - Hook integration points
5. **AOIIntegration.cs** - Integration layer
6. **AOITest.cs** - Testing framework
7. **Hooks_PlayerMove.cs** - Modified để tích hợp AOI
8. **Players.CrossServerZone.cs** - Modified để tích hợp AOI
9. **MainWindow.axaml.cs** - Modified để khởi tạo AOI
10. **ClientActor.cs** - Modified để tích hợp player login
11. **TcpManagerActor.cs** - Modified để tích hợp player logout
12. **ActorNetState.cs** - Modified để tích hợp session cleanup

## Kết luận

Hệ thống AOI mới cung cấp:
- ✅ Performance tốt hơn đáng kể
- ✅ Scalability cho nhiều players
- ✅ Giải quyết grid boundary issues
- ✅ Incremental updates
- ✅ Backward compatibility
- ✅ Easy monitoring và debugging
- ✅ **Đã được tích hợp hoàn toàn vào server**

Hệ thống AOI hiện đã sẵn sàng sử dụng và sẽ tự động hoạt động khi server khởi động!
