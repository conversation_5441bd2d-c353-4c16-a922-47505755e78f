using System;
using System.Collections.Generic;
using HeroYulgang.Helpers;
using RxjhServer.Neo;
using RxjhServer.Network;

namespace RxjhServer
{
    /// <summary>
    /// Extension methods cho NpcClass để tích hợp AOI system
    /// </summary>
    public partial class NpcClass
    {
        /// <summary>
        /// Khởi tạo NPC trong AOI system
        /// </summary>
        public void InitializeAOI()
        {
            try
            {
                AOIManager.Instance.AddNPC(this);
                LogHelper.WriteLine(LogLevel.Debug, 
                    $"NPC {NPC_SessionID} (PID: {FLD_PID}) initialized in AOI system at [{Rxjh_X},{Rxjh_Y}] on map {Rxjh_Map}");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, 
                    $"InitializeAOI error for NPC {NPC_SessionID}: {ex.Message}");
            }
        }

        /// <summary>
        /// Cleanup NPC khỏi AOI system
        /// </summary>
        public void CleanupAOI()
        {
            try
            {
                AOIManager.Instance.RemoveNPC(NPC_SessionID);
                LogHelper.WriteLine(LogLevel.Debug, 
                    $"NPC {NPC_SessionID} (PID: {FLD_PID}) removed from AOI system");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, 
                    $"CleanupAOI error for NPC {NPC_SessionID}: {ex.Message}");
            }
        }

        /// <summary>
        /// Cập nhật vị trí NPC trong AOI system
        /// </summary>
        public void UpdatePositionAOI(float newX, float newY, float newZ)
        {
            try
            {
                var oldX = Rxjh_X;
                var oldY = Rxjh_Y;
                
                // Cập nhật tọa độ
                Rxjh_X = newX;
                Rxjh_Y = newY;
                Rxjh_Z = newZ;

                // Kiểm tra xem có thay đổi grid không
                var oldGridPos = AOIManager.Instance.GetGridPosition(oldX, oldY);
                var newGridPos = AOIManager.Instance.GetGridPosition(newX, newY);

                if (oldGridPos.gridX != newGridPos.gridX || oldGridPos.gridY != newGridPos.gridY)
                {
                    // NPC đã chuyển grid, cần cập nhật AOI
                    CleanupAOI();
                    InitializeAOI();
                    
                    LogHelper.WriteLine(LogLevel.Debug, 
                        $"NPC {NPC_SessionID} moved from grid [{oldGridPos.gridX},{oldGridPos.gridY}] to [{newGridPos.gridX},{newGridPos.gridY}]");
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, 
                    $"UpdatePositionAOI error for NPC {NPC_SessionID}: {ex.Message}");
            }
        }

        /// <summary>
        /// Gửi update data tới tất cả players trong phạm vi sử dụng AOI
        /// Thay thế cho SendCurrentRangeBroadcastData() cũ
        /// </summary>
        public void SendCurrentRangeBroadcastDataAOI(SendingClass sendingClass, int packetType, int sessionId)
        {
            try
            {
                // Lấy tất cả grids lân cận
                var nearbyGrids = AOIManager.Instance.GetNearbyGrids(Rxjh_Map, Rxjh_X, Rxjh_Y);
                
                foreach (var grid in nearbyGrids)
                {
                    foreach (var player in grid.GetPlayers())
                    {
                        if (player.Client != null && !player.Client.TreoMay)
                        {
                            // Kiểm tra khoảng cách chính xác
                            if (FindPlayers(AOIManager.VISIBILITY_RANGE, player))
                            {
                                player.Client?.SendPak(sendingClass, packetType, sessionId);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, 
                    $"SendCurrentRangeBroadcastDataAOI error for NPC {NPC_SessionID}: {ex.Message}");
            }
        }

        /// <summary>
        /// Gửi packet tới tất cả players trong phạm vi sử dụng AOI
        /// </summary>
        public void SendToNearbyPlayersAOI(byte[] packetData, int packetLength)
        {
            try
            {
                // Lấy tất cả grids lân cận
                var nearbyGrids = AOIManager.Instance.GetNearbyGrids(Rxjh_Map, Rxjh_X, Rxjh_Y);
                
                foreach (var grid in nearbyGrids)
                {
                    foreach (var player in grid.GetPlayers())
                    {
                        if (player.Client != null && !player.Client.TreoMay)
                        {
                            // Kiểm tra khoảng cách chính xác
                            if (FindPlayers(AOIManager.VISIBILITY_RANGE, player))
                            {
                                player.Client.Send_Map_Data(packetData, packetLength);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, 
                    $"SendToNearbyPlayersAOI error for NPC {NPC_SessionID}: {ex.Message}");
            }
        }

       
        /// <summary>
        /// Lấy tất cả players trong phạm vi của NPC sử dụng AOI
        /// </summary>
        public List<Players> GetNearbyPlayersAOI(int range = -1)
        {
            var result = new List<Players>();
            
            try
            {
                if (range == -1)
                    range = AOIManager.VISIBILITY_RANGE;

                // Lấy tất cả grids lân cận
                var nearbyGrids = AOIManager.Instance.GetNearbyGrids(Rxjh_Map, Rxjh_X, Rxjh_Y);
                
                foreach (var grid in nearbyGrids)
                {
                    foreach (var player in grid.GetPlayers())
                    {
                        if (FindPlayers(range, player))
                        {
                            result.Add(player);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, 
                    $"GetNearbyPlayersAOI error for NPC {NPC_SessionID}: {ex.Message}");
            }
            
            return result;
        }

        /// <summary>
        /// Kiểm tra có player nào trong phạm vi không sử dụng AOI
        /// </summary>
        public bool HasPlayersInRangeAOI(int range = -1)
        {
            try
            {
                if (range == -1)
                    range = AOIManager.VISIBILITY_RANGE;

                // Lấy tất cả grids lân cận
                var nearbyGrids = AOIManager.Instance.GetNearbyGrids(Rxjh_Map, Rxjh_X, Rxjh_Y);
                
                foreach (var grid in nearbyGrids)
                {
                    foreach (var player in grid.GetPlayers())
                    {
                        if (FindPlayers(range, player))
                        {
                            return true;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, 
                    $"HasPlayersInRangeAOI error for NPC {NPC_SessionID}: {ex.Message}");
            }
            
            return false;
        }

        /// <summary>
        /// Thông báo NPC death sử dụng AOI
        /// </summary>
        public void NotifyDeathAOI()
        {
            try
            {
                using SendingClass pak = new();
                
                // Lấy tất cả grids lân cận
                var nearbyGrids = AOIManager.Instance.GetNearbyGrids(Rxjh_Map, Rxjh_X, Rxjh_Y);
                
                foreach (var grid in nearbyGrids)
                {
                    foreach (var player in grid.GetPlayers())
                    {
                        if (player.Client != null && !player.Client.TreoMay)
                        {
                            // Kiểm tra khoảng cách chính xác
                            if (FindPlayers(AOIManager.VISIBILITY_RANGE, player))
                            {
                                player.Client?.SendPak(pak, 34816, NPC_SessionID);
                            }
                        }
                    }
                }
                
                LogHelper.WriteLine(LogLevel.Debug, 
                    $"NPC {NPC_SessionID} death notification sent via AOI");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, 
                    $"NotifyDeathAOI error for NPC {NPC_SessionID}: {ex.Message}");
            }
        }
    }
}
