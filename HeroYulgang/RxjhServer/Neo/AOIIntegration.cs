using System;
using HeroYulgang.Helpers;
using RxjhServer.Neo;

namespace RxjhServer.Neo
{
    /// <summary>
    /// Class để tích hợp AOI system vào các điểm khác nhau trong game server
    /// </summary>
    public static class AOIIntegration
    {
        /// <summary>
        /// Khởi tạo AOI system khi server startup
        /// G<PERSON><PERSON> từ Main() hoặc server initialization
        /// </summary>
        public static void InitializeOnServerStartup()
        {
            try
            {
                LogHelper.WriteLine(LogLevel.Info, "=== Initializing AOI System ===");

                // Khởi tạo AOI system
                AOIHooks.OnServerStartup();

                LogHelper.WriteLine(LogLevel.Info, "AOI System initialization completed successfully");

                // Chạy basic test để đảm bảo system hoạt động
                try
                {
                    AOITest.RunBasicTest();
                }
                catch (Exception testEx)
                {
                    LogHelper.WriteLine(LogLevel.Warning,
                        $"AOI Test warning: {testEx.Message}");
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Failed to initialize AOI System: {ex.Message}");
            }
        }

        /// <summary>
        /// Hook vào player login process
        /// Gọi từ Players.Login() hoặc authentication success
        /// </summary>
        public static void OnPlayerLogin(Players player)
        {
            try
            {
                if (player == null) return;

                AOIHooks.OnPlayerLogin(player);

                // Trigger initial AOI updates
                player.GetTheReviewRangePlayers();
                player.GetReviewScopeNpc();
                player.ThuThap_VatPham_Drop_PhamVi();
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error,
                    $"AOI Integration error on player login {player?.CharacterName}: {ex.Message}");
            }
        }

        /// <summary>
        /// Hook vào player logout process
        /// Gọi từ Players.Disconnect() hoặc session cleanup
        /// </summary>
        public static void OnPlayerLogout(Players player)
        {
            try
            {
                if (player == null) return;

                AOIHooks.OnPlayerLogout(player);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error,
                    $"AOI Integration error on player logout {player?.CharacterName}: {ex.Message}");
            }
        }

        /// <summary>
        /// Hook vào player movement
        /// Gọi từ movement packet handlers
        /// </summary>
        public static void OnPlayerMove(Players player, float newX, float newY, float newZ)
        {
            try
            {
                if (player == null) return;

                AOIHooks.OnPlayerMove(player, newX, newY, newZ);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error,
                    $"AOI Integration error on player move {player?.CharacterName}: {ex.Message}");
            }
        }

        /// <summary>
        /// Hook vào CharacterMove packet handler
        /// Gọi từ Players.CharacterMove()
        /// </summary>
        public static void OnCharacterMove(Players player, byte[] data, int length)
        {
            try
            {
                if (player == null || data == null || length < 30) return;

                // Extract coordinates từ packet data
                // Packet structure: [header][sessionid][...][x][z][y][...]
                float newX = BitConverter.ToSingle(data, 14);
                float newZ = BitConverter.ToSingle(data, 18);
                float newY = BitConverter.ToSingle(data, 22);

                // Gọi AOI hook
                OnPlayerMove(player, newX, newY, newZ);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error,
                    $"AOI Integration error on character move {player?.CharacterName}: {ex.Message}");
            }
        }

        /// <summary>
        /// Hook vào player teleport/map change
        /// Gọi từ teleport hoặc map change handlers
        /// </summary>
        public static void OnPlayerMapChange(Players player, int oldMapId, int newMapId)
        {
            try
            {
                if (player == null) return;

                AOIHooks.OnPlayerMapChange(player, oldMapId, newMapId);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error,
                    $"AOI Integration error on player map change {player?.CharacterName}: {ex.Message}");
            }
        }

        /// <summary>
        /// Hook vào NPC spawn
        /// Gọi từ NPC creation/respawn
        /// </summary>
        public static void OnNPCSpawn(NpcClass npc)
        {
            try
            {
                if (npc == null) return;

                AOIHooks.OnNPCSpawn(npc);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error,
                    $"AOI Integration error on NPC spawn {npc?.NPC_SessionID}: {ex.Message}");
            }
        }

        /// <summary>
        /// Hook vào NPC death/despawn
        /// Gọi từ NPC death handlers
        /// </summary>
        public static void OnNPCDespawn(NpcClass npc)
        {
            try
            {
                if (npc == null) return;

                AOIHooks.OnNPCDespawn(npc);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error,
                    $"AOI Integration error on NPC despawn {npc?.NPC_SessionID}: {ex.Message}");
            }
        }

        /// <summary>
        /// Hook vào item drop
        /// Gọi từ item drop handlers
        /// </summary>
        public static void OnItemDrop(X_Mat_Dat_Vat_Pham_Loai item)
        {
            try
            {
                if (item == null) return;

                AOIHooks.OnItemDrop(item);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error,
                    $"AOI Integration error on item drop {item?.id}: {ex.Message}");
            }
        }

        /// <summary>
        /// Hook vào item pickup/removal
        /// Gọi từ item pickup handlers
        /// </summary>
        public static void OnItemRemove(long itemId)
        {
            try
            {
                AOIHooks.OnItemRemove(itemId);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error,
                    $"AOI Integration error on item remove {itemId}: {ex.Message}");
            }
        }

        /// <summary>
        /// Periodic maintenance cho AOI system
        /// Gọi từ server maintenance timer
        /// </summary>
        public static void PeriodicMaintenance()
        {
            try
            {
                // Validate và repair AOI system nếu cần
                World.ValidateAndRepairAOI();

                // Log statistics
                var stats = World.GetAOIStatistics();
                LogHelper.WriteLine(LogLevel.Debug, $"AOI Maintenance: {stats}");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error,
                    $"AOI Integration error during maintenance: {ex.Message}");
            }
        }

        /// <summary>
        /// Enable/disable AOI system
        /// Có thể gọi từ admin commands
        /// </summary>
        public static void SetAOIEnabled(bool enabled)
        {
            try
            {
                AOIHooks.SetAOIEnabled(enabled);

                if (enabled)
                {
                    // Re-initialize nếu cần
                    World.InitializeAOISystem();

                    // Refresh tất cả players
                    AOIHooks.RefreshAllPlayersAOI();
                }

                LogHelper.WriteLine(LogLevel.Info,
                    $"AOI System {(enabled ? "enabled" : "disabled")} via integration");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error,
                    $"AOI Integration error setting enabled state: {ex.Message}");
            }
        }

        /// <summary>
        /// Lấy thống kê AOI system
        /// </summary>
        public static string GetAOIStatistics()
        {
            try
            {
                return World.GetAOIStatistics();
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error,
                    $"AOI Integration error getting statistics: {ex.Message}");
                return "AOI Statistics - Error retrieving data";
            }
        }

        /// <summary>
        /// Admin command để kiểm tra AOI status
        /// </summary>
        public static string GetAOIStatus()
        {
            try
            {
                var hookStatus = AOIHooks.GetAOIStatus();
                var managerStats = AOIManager.Instance.GetStatistics();

                return $"{hookStatus}\n{managerStats}";
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error,
                    $"AOI Integration error getting status: {ex.Message}");
                return "AOI Status - Error retrieving data";
            }
        }

        /// <summary>
        /// Force refresh AOI cho một player cụ thể
        /// </summary>
        public static void RefreshPlayerAOI(Players player)
        {
            try
            {
                if (player == null) return;

                player.GetTheReviewRangePlayers();
                player.GetReviewScopeNpc();
                player.ThuThap_VatPham_Drop_PhamVi();

                LogHelper.WriteLine(LogLevel.Debug,
                    $"AOI refreshed for player {player.CharacterName}");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error,
                    $"AOI Integration error refreshing player {player?.CharacterName}: {ex.Message}");
            }
        }

        /// <summary>
        /// Cleanup AOI cho một map cụ thể
        /// Gọi khi map được unload
        /// </summary>
        public static void CleanupMap(int mapId)
        {
            try
            {
                AOIHooks.OnMapCleanup(mapId);

                LogHelper.WriteLine(LogLevel.Info,
                    $"AOI cleanup completed for map {mapId}");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error,
                    $"AOI Integration error cleaning up map {mapId}: {ex.Message}");
            }
        }
    }
}
