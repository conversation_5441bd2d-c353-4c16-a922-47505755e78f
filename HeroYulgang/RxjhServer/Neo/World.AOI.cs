using System;
using System.Linq;
using HeroYulgang.Helpers;
using RxjhServer.Neo;

namespace RxjhServer
{
    /// <summary>
    /// Extension methods cho World class để tích hợp AOI system
    /// </summary>
    public partial class World
    {
        /// <summary>
        /// Khởi tạo AOI system cho tất cả maps hiện có
        /// </summary>
        public static void InitializeAOISystem()
        {
            try
            {
                LogHelper.WriteLine(LogLevel.Info, "Initializing AOI System...");

                // Khởi tạo grids cho tất cả maps hiện có
                foreach (var mapId in MapList.Keys)
                {
                    AOIManager.Instance.InitializeMapGrids(mapId);
                }

                // Thêm tất cả NPCs hiện có vào AOI system
                InitializeNPCsInAOI();

                // Thêm tất cả items hiện có vào AOI system
                InitializeItemsInAOI();

                LogHelper.WriteLine(LogLevel.Info, 
                    $"AOI System initialized successfully. {AOIManager.Instance.GetStatistics()}");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Failed to initialize AOI System: {ex.Message}");
            }
        }

        /// <summary>
        /// Khởi tạo tất cả NPCs trong AOI system
        /// </summary>
        private static void InitializeNPCsInAOI()
        {
            try
            {
                int npcCount = 0;

                foreach (var mapClass in MapList.Values)
                {
                    foreach (var npc in mapClass.npcTemplate.Values)
                    {
                        npc.InitializeAOI();
                        npcCount++;
                    }
                }

                LogHelper.WriteLine(LogLevel.Info, $"Initialized {npcCount} NPCs in AOI system");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"InitializeNPCsInAOI error: {ex.Message}");
            }
        }

        /// <summary>
        /// Khởi tạo tất cả items trong AOI system
        /// </summary>
        private static void InitializeItemsInAOI()
        {
            try
            {
                int itemCount = 0;

                foreach (var item in ItmeTeM.Values)
                {
                    item.InitializeAOI();
                    itemCount++;
                }

                LogHelper.WriteLine(LogLevel.Info, $"Initialized {itemCount} items in AOI system");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"InitializeItemsInAOI error: {ex.Message}");
            }
        }

        /// <summary>
        /// Thêm player mới vào AOI system
        /// </summary>
        public static void AddPlayerToAOI(Players player)
        {
            try
            {
                if (player == null) return;

                // Khởi tạo map grids nếu chưa có
                AOIManager.Instance.InitializeMapGrids(player.NhanVatToaDo_BanDo);

                // Thêm player vào AOI
                player.InitializeAOI();

                LogHelper.WriteLine(LogLevel.Debug, 
                    $"Player {player.CharacterName} added to AOI system");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, 
                    $"AddPlayerToAOI error for player {player?.CharacterName}: {ex.Message}");
            }
        }

        /// <summary>
        /// Xóa player khỏi AOI system
        /// </summary>
        public static void RemovePlayerFromAOI(Players player)
        {
            try
            {
                if (player == null) return;

                player.CleanupAOI();

                LogHelper.WriteLine(LogLevel.Debug, 
                    $"Player {player.CharacterName} removed from AOI system");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, 
                    $"RemovePlayerFromAOI error for player {player?.CharacterName}: {ex.Message}");
            }
        }

        /// <summary>
        /// Thêm NPC mới vào AOI system
        /// </summary>
        public static void AddNPCToAOI(NpcClass npc)
        {
            try
            {
                if (npc == null) return;

                // Khởi tạo map grids nếu chưa có
                AOIManager.Instance.InitializeMapGrids(npc.Rxjh_Map);

                // Thêm NPC vào AOI
                npc.InitializeAOI();

                LogHelper.WriteLine(LogLevel.Debug, 
                    $"NPC {npc.NPC_SessionID} (PID: {npc.FLD_PID}) added to AOI system");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, 
                    $"AddNPCToAOI error for NPC {npc?.NPC_SessionID}: {ex.Message}");
            }
        }

        /// <summary>
        /// Xóa NPC khỏi AOI system
        /// </summary>
        public static void RemoveNPCFromAOI(NpcClass npc)
        {
            try
            {
                if (npc == null) return;

                npc.CleanupAOI();

                LogHelper.WriteLine(LogLevel.Debug, 
                    $"NPC {npc.NPC_SessionID} (PID: {npc.FLD_PID}) removed from AOI system");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, 
                    $"RemoveNPCFromAOI error for NPC {npc?.NPC_SessionID}: {ex.Message}");
            }
        }

        /// <summary>
        /// Thêm item mới vào AOI system
        /// </summary>
        public static void AddItemToAOI(X_Mat_Dat_Vat_Pham_Loai item)
        {
            try
            {
                if (item == null) return;

                // Khởi tạo map grids nếu chưa có
                AOIManager.Instance.InitializeMapGrids(item.Rxjh_Map);

                // Sử dụng AOIItemManager để thêm item
                AOIItemManager.AddItemToWorld(item);

                LogHelper.WriteLine(LogLevel.Debug, 
                    $"Item {item.id} added to AOI system");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, 
                    $"AddItemToAOI error for item {item?.id}: {ex.Message}");
            }
        }

        /// <summary>
        /// Xóa item khỏi AOI system
        /// </summary>
        public static void RemoveItemFromAOI(long itemId)
        {
            try
            {
                AOIItemManager.RemoveItemFromWorld(itemId);

                LogHelper.WriteLine(LogLevel.Debug, 
                    $"Item {itemId} removed from AOI system");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, 
                    $"RemoveItemFromAOI error for item {itemId}: {ex.Message}");
            }
        }

        /// <summary>
        /// Cleanup AOI data cho một map
        /// </summary>
        public static void CleanupMapAOI(int mapId)
        {
            try
            {
                // Cleanup items
                AOIItemManager.CleanupMapItems(mapId);

                // Cleanup AOI grids
                AOIManager.Instance.ClearMap(mapId);

                LogHelper.WriteLine(LogLevel.Info, 
                    $"AOI data cleaned up for map {mapId}");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, 
                    $"CleanupMapAOI error for map {mapId}: {ex.Message}");
            }
        }

        /// <summary>
        /// Lấy thống kê chi tiết về AOI system
        /// </summary>
        public static string GetAOIStatistics()
        {
            try
            {
                var aoiStats = AOIManager.Instance.GetStatistics();
                var itemStats = AOIItemManager.GetItemStatistics();
                var playerCount = allConnectedChars?.Count ?? 0;
                
                return $"=== AOI System Statistics ===\n" +
                       $"{aoiStats}\n" +
                       $"Connected Players: {playerCount}\n" +
                       $"{itemStats}\n" +
                       $"Maps with AOI: {MapList.Count}";
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"GetAOIStatistics error: {ex.Message}");
                return "AOI Statistics - Error retrieving data";
            }
        }

        /// <summary>
        /// Kiểm tra và sửa chữa AOI system nếu cần
        /// </summary>
        public static void ValidateAndRepairAOI()
        {
            try
            {
                LogHelper.WriteLine(LogLevel.Info, "Validating AOI system...");

                int repairedPlayers = 0;
                int repairedNPCs = 0;
                int repairedItems = 0;

                // Kiểm tra players
                foreach (var player in allConnectedChars.Values)
                {
                    try
                    {
                        var aoiResult = AOIManager.Instance.UpdatePlayerPosition(player);
                        if (aoiResult.HasChanges)
                        {
                            repairedPlayers++;
                        }
                    }
                    catch (Exception ex)
                    {
                        LogHelper.WriteLine(LogLevel.Warning, 
                            $"Failed to validate player {player.CharacterName}: {ex.Message}");
                    }
                }

                // Kiểm tra NPCs
                foreach (var mapClass in MapList.Values)
                {
                    foreach (var npc in mapClass.npcTemplate.Values)
                    {
                        try
                        {
                            // Re-initialize NPC in AOI if needed
                            npc.CleanupAOI();
                            npc.InitializeAOI();
                            repairedNPCs++;
                        }
                        catch (Exception ex)
                        {
                            LogHelper.WriteLine(LogLevel.Warning, 
                                $"Failed to validate NPC {npc.NPC_SessionID}: {ex.Message}");
                        }
                    }
                }

                // Kiểm tra items
                foreach (var item in ItmeTeM.Values)
                {
                    try
                    {
                        // Re-initialize item in AOI if needed
                        item.CleanupAOI();
                        item.InitializeAOI();
                        repairedItems++;
                    }
                    catch (Exception ex)
                    {
                        LogHelper.WriteLine(LogLevel.Warning, 
                            $"Failed to validate item {item.id}: {ex.Message}");
                    }
                }

                LogHelper.WriteLine(LogLevel.Info, 
                    $"AOI validation completed. Repaired: {repairedPlayers} players, {repairedNPCs} NPCs, {repairedItems} items");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"ValidateAndRepairAOI error: {ex.Message}");
            }
        }
    }
}
