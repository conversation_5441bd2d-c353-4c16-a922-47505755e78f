using System;
using System.Collections.Generic;
using System.Collections.Concurrent;
using HeroYulgang.Helpers;

namespace RxjhServer.Neo
{
    /// <summary>
    /// Đ<PERSON><PERSON> diện cho một grid trong hệ thống AOI
    /// Mỗi grid chứa các entities (players, NPCs, items) trong phạm vi của nó
    /// </summary>
    public class AOIGrid
    {
        private readonly object _lock = new object();
        
        public int GridX { get; }
        public int GridY { get; }
        public int MapId { get; }
        
        // Thread-safe collections để lưu trữ entities
        private readonly ConcurrentDictionary<int, Players> _players = new();
        private readonly ConcurrentDictionary<int, NpcClass> _npcs = new();
        private readonly ConcurrentDictionary<long, X_Mat_Dat_Vat_Pham_Loai> _items = new();
        
        // Boundaries của grid này
        public float MinX { get; }
        public float MaxX { get; }
        public float MinY { get; }
        public float MaxY { get; }
        
        public AOIGrid(int gridX, int gridY, int mapId, float minX, float maxX, float minY, float maxY)
        {
            GridX = gridX;
            GridY = gridY;
            MapId = mapId;
            MinX = minX;
            MaxX = maxX;
            MinY = minY;
            MaxY = maxY;
        }
        
        #region Player Management
        
        /// <summary>
        /// Thêm player vào grid
        /// </summary>
        public bool AddPlayer(Players player)
        {
            if (player == null) return false;
            
            try
            {
                return _players.TryAdd(player.SessionID, player);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"AOIGrid.AddPlayer error: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// Xóa player khỏi grid
        /// </summary>
        public bool RemovePlayer(int sessionId)
        {
            try
            {
                return _players.TryRemove(sessionId, out _);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"AOIGrid.RemovePlayer error: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// Lấy tất cả players trong grid
        /// </summary>
        public ICollection<Players> GetPlayers()
        {
            return _players.Values;
        }
        
        /// <summary>
        /// Kiểm tra player có trong grid không
        /// </summary>
        public bool ContainsPlayer(int sessionId)
        {
            return _players.ContainsKey(sessionId);
        }
        
        #endregion
        
        #region NPC Management
        
        /// <summary>
        /// Thêm NPC vào grid
        /// </summary>
        public bool AddNPC(NpcClass npc)
        {
            if (npc == null) return false;
            
            try
            {
                return _npcs.TryAdd(npc.NPC_SessionID, npc);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"AOIGrid.AddNPC error: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// Xóa NPC khỏi grid
        /// </summary>
        public bool RemoveNPC(int npcSessionId)
        {
            try
            {
                return _npcs.TryRemove(npcSessionId, out _);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"AOIGrid.RemoveNPC error: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// Lấy tất cả NPCs trong grid
        /// </summary>
        public ICollection<NpcClass> GetNPCs()
        {
            return _npcs.Values;
        }
        
        /// <summary>
        /// Kiểm tra NPC có trong grid không
        /// </summary>
        public bool ContainsNPC(int npcSessionId)
        {
            return _npcs.ContainsKey(npcSessionId);
        }
        
        #endregion
        
        #region Item Management
        
        /// <summary>
        /// Thêm item vào grid
        /// </summary>
        public bool AddItem(X_Mat_Dat_Vat_Pham_Loai item)
        {
            if (item == null) return false;
            
            try
            {
                return _items.TryAdd(item.id, item);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"AOIGrid.AddItem error: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// Xóa item khỏi grid
        /// </summary>
        public bool RemoveItem(long itemId)
        {
            try
            {
                return _items.TryRemove(itemId, out _);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"AOIGrid.RemoveItem error: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// Lấy tất cả items trong grid
        /// </summary>
        public ICollection<X_Mat_Dat_Vat_Pham_Loai> GetItems()
        {
            return _items.Values;
        }
        
        /// <summary>
        /// Kiểm tra item có trong grid không
        /// </summary>
        public bool ContainsItem(long itemId)
        {
            return _items.ContainsKey(itemId);
        }
        
        #endregion
        
        #region Utility Methods
        
        /// <summary>
        /// Kiểm tra một tọa độ có nằm trong grid này không
        /// </summary>
        public bool ContainsPosition(float x, float y)
        {
            return x >= MinX && x <= MaxX && y >= MinY && y <= MaxY;
        }
        
        /// <summary>
        /// Lấy số lượng entities trong grid
        /// </summary>
        public (int players, int npcs, int items) GetEntityCounts()
        {
            return (_players.Count, _npcs.Count, _items.Count);
        }
        
        /// <summary>
        /// Clear tất cả entities khỏi grid
        /// </summary>
        public void Clear()
        {
            _players.Clear();
            _npcs.Clear();
            _items.Clear();
        }
        
        public override string ToString()
        {
            var counts = GetEntityCounts();
            return $"Grid[{GridX},{GridY}] Map:{MapId} - Players:{counts.players}, NPCs:{counts.npcs}, Items:{counts.items}";
        }
        
        #endregion
    }
}
