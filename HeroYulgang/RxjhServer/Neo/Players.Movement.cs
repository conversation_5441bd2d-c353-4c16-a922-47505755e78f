using System;
using HeroYulgang.Helpers;

namespace RxjhServer
{
    /// <summary>
    /// Extension methods cho Players class để tích hợp AOI vào movement
    /// </summary>
    public partial class Players
    {
        /// <summary>
        /// Hook vào CharacterMove để tích hợp AOI system
        /// Gọi method này từ CharacterMove packet handler
        /// </summary>
        public void CharacterMoveWithAOI(byte[] data, int length)
        {
            try
            {
                // Gọi AOI integration trước khi xử lý movement
                AOIIntegration.OnCharacterMove(this, data, length);
                
                // Gọi CharacterMove gốc
                CharacterMove(data, length);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, 
                    $"CharacterMoveWithAOI error for player {CharacterName}: {ex.Message}");
                
                // Fallback to original CharacterMove
                try
                {
                    CharacterMove(data, length);
                }
                catch (Exception fallbackEx)
                {
                    LogHelper.WriteLine(LogLevel.Error, 
                        $"Fallback CharacterMove error for player {CharacterName}: {fallbackEx.Message}");
                }
            }
        }

        /// <summary>
        /// Hook vào Mobile để tích hợp AOI system
        /// Gọi method này từ teleport/map change
        /// </summary>
        public void MobileWithAOI(float x, float y, float z, int mapId, int type)
        {
            try
            {
                // Lưu map cũ để xử lý map change
                int oldMapId = NhanVatToaDo_BanDo;
                
                // Gọi Mobile gốc trước
                Mobile(x, y, z, mapId, type);
                
                // Nếu đổi map, xử lý AOI map change
                if (oldMapId != mapId)
                {
                    AOIIntegration.OnPlayerMapChange(this, oldMapId, mapId);
                }
                else
                {
                    // Nếu chỉ teleport trong cùng map, xử lý movement
                    AOIIntegration.OnPlayerMove(this, x, y, z);
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, 
                    $"MobileWithAOI error for player {CharacterName}: {ex.Message}");
                
                // Fallback to original Mobile
                try
                {
                    Mobile(x, y, z, mapId, type);
                }
                catch (Exception fallbackEx)
                {
                    LogHelper.WriteLine(LogLevel.Error, 
                        $"Fallback Mobile error for player {CharacterName}: {fallbackEx.Message}");
                }
            }
        }

        /// <summary>
        /// Hook vào NPC transmission để tích hợp AOI system
        /// </summary>
        public void NpcTransmissionWithAOI(byte[] data, int length)
        {
            try
            {
                // Lưu thông tin trước khi teleport
                int oldMapId = NhanVatToaDo_BanDo;
                float oldX = NhanVatToaDo_X;
                float oldY = NhanVatToaDo_Y;
                float oldZ = NhanVatToaDo_Z;
                
                // Gọi NpcTransmission gốc
                NpcTransmission(data, length);
                
                // Kiểm tra xem có thay đổi vị trí không
                if (oldMapId != NhanVatToaDo_BanDo)
                {
                    // Map change
                    AOIIntegration.OnPlayerMapChange(this, oldMapId, NhanVatToaDo_BanDo);
                }
                else if (oldX != NhanVatToaDo_X || oldY != NhanVatToaDo_Y || oldZ != NhanVatToaDo_Z)
                {
                    // Position change trong cùng map
                    AOIIntegration.OnPlayerMove(this, NhanVatToaDo_X, NhanVatToaDo_Y, NhanVatToaDo_Z);
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, 
                    $"NpcTransmissionWithAOI error for player {CharacterName}: {ex.Message}");
                
                // Fallback to original NpcTransmission
                try
                {
                    NpcTransmission(data, length);
                }
                catch (Exception fallbackEx)
                {
                    LogHelper.WriteLine(LogLevel.Error, 
                        $"Fallback NpcTransmission error for player {CharacterName}: {fallbackEx.Message}");
                }
            }
        }

        /// <summary>
        /// Hook vào KhinhCong (lightness skill) để tích hợp AOI system
        /// </summary>
        public void KhinhCongWithAOI(byte[] data, int length)
        {
            try
            {
                // Lưu thông tin trước khi sử dụng khinh công
                float oldX = NhanVatToaDo_X;
                float oldY = NhanVatToaDo_Y;
                float oldZ = NhanVatToaDo_Z;
                
                // Gọi KhinhCong gốc
                KhinhCong(data, length);
                
                // Kiểm tra xem có thay đổi vị trí không
                if (oldX != NhanVatToaDo_X || oldY != NhanVatToaDo_Y || oldZ != NhanVatToaDo_Z)
                {
                    AOIIntegration.OnPlayerMove(this, NhanVatToaDo_X, NhanVatToaDo_Y, NhanVatToaDo_Z);
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, 
                    $"KhinhCongWithAOI error for player {CharacterName}: {ex.Message}");
                
                // Fallback to original KhinhCong
                try
                {
                    KhinhCong(data, length);
                }
                catch (Exception fallbackEx)
                {
                    LogHelper.WriteLine(LogLevel.Error, 
                        $"Fallback KhinhCong error for player {CharacterName}: {fallbackEx.Message}");
                }
            }
        }
    }
}
