using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Timers;
using HeroYulgang.Helpers;
using RxjhServer.Database;
using RxjhServer.HelperTools;
using RxjhServer.Network;
using RxjhServer.ManageZone;
using HeroYulgang.Services;

namespace RxjhServer;

public partial class NpcClass : IDisposable
{
	public Zone CurrentZone { get; set; }
	public int ID { get; set; }
	public bool IsWorldBoss { get ; set; }
	private bool isDoingMech = false;
	private static PlayGjClass asfd = new();

	private object AsyncLocksw = new();

	private ArrayList tem = new();

	public Reverser<PlayGjClass> reverser = new(asfd.GetType(), "Gjsl", ReverserInfo.Direction.DESC);

	public List<PlayGjClass> PlayerTargetList = new();

	private ThreadSafeDictionary<int, Players> PlayList;

	public System.Timers.Timer AutomaticMove;
	public int MarkedType = 0;
	   public double DisPoseTime { get; internal set; }
	public bool NPC_Removed = false;
	public DateTime timeNpcRevival = DateTime.MinValue;
	public DateTime timeNpcDie = DateTime.Now;

	public System.Timers.Timer AutomaticAttack;

	public System.Timers.Timer TuDongHoiSinh;

	//public System.Timers.Timer Tu_Vong_Tri_Hoan_Bien_Mat_Dong_Ho;

	private static Random Ran;

	public double FirstTimeISeeYou_X = -1E+17;

	public double FirstTimeISeeYou_Y = -1E+17;

	public double FirstTimeISeeYou_Z = -1E+17;

	public int dame_gayboi_thannu_khidanhbom = 0;

	public Dictionary<int, X_Di_Thuong_Trang_Thai_Loai> TrangThai_BatThuong;

	public Dictionary<int, X_Di_Thuong_Mat_Mau_Trang_Thai_Loai> TrangThai_MatMau_BatThuong;

	private X_Linh_Thu_Loai _PlayCw;

	private float _FLD_FACE1;

	private float _FLD_FACE2;

	private int _IsNpc;

	private string _Name;

	private int _FLD_INDEX;

	private int _FLD_PID;

	private double _FLD_AT;

	private float _Rxjh_X;

	private float _Rxjh_Y;

	private float _Rxjh_Z;

	private float _Rxjh_cs_X;

	private float _Rxjh_cs_Y;

	private float _Rxjh_cs_Z;

	private int _Rxjh_Map;

	private int _Rxjh_Exp;

	private int _Max_Rxjh_HP;

	private int _Rxjh_HP;

	private int _Level;

	private double _FLD_DF;

	private string _WorldId;

	private int _FLD_AUTO;

	private int _FLD_BOSS;

	private int _FLD_NEWTIME;

	private bool _NPCDeath;

	private bool _QuaiXuatHien_DuyNhatMotLan;

	public int FLD_HieuUngGiamDef_Ninja = 0;

	private bool DiTinh_MissDame;

	public int 怪物数字;

	public Dictionary<int, Players> templayer = new();

	public DateTime timeNpc_HoiSinh = DateTime.MinValue;

	public int dch_towerflag;

	public Players players_quaidanh;
	private bool isCastingMagic;
	private int attackCount;

	public int DCH_Tower
	{
		get
		{
			return dch_towerflag;
		}
		set
		{
			dch_towerflag = value;
		}
	}

	public X_Linh_Thu_Loai PlayCw
	{
		get
		{
			return _PlayCw;
		}
		set
		{
			_PlayCw = value;
		}
	}

	public ThreadSafeDictionary<int, Players> _playlist => PlayList;

	public List<PlayGjClass> PlayGj
	{
		get
		{
			return PlayerTargetList;
		}
		set
		{
			PlayerTargetList = value;
		}
	}

	public int PlayerWid
	{
		get
		{
			if (PlayerTargetList.Count <= 0)
			{
				return 0;
			}
			try
			{
				if (PlayerTargetList[0].PlayID != 0)
				{
					PlayerTargetList.Sort(new Reverser<PlayGjClass>(new PlayGjClass().GetType(), "Gjsl", ReverserInfo.Direction.DESC));
					return PlayerTargetList[0].PlayID;
				}
				return 0;
			}
			catch (Exception)
			{
				return 0;
			}
		}
	}
	public System.Timers.Timer AutomaticDisPose ;

	public int BossPlayerWid
	{
		get
		{
			if (PlayerTargetList.Count <= 0)
			{
				return 0;
			}
			try
			{
				var num = 0;
				int index;
				while (true)
				{
					index = RNG.Next(0, PlayerTargetList.Count - 1);
					if (PlayerTargetList[index].Gjxl >= 100000)
					{
						break;
					}
					num++;
					if (num >= PlayerTargetList.Count)
					{
						PlayerTargetList.Sort(new Reverser<PlayGjClass>(new PlayGjClass().GetType(), "Gjsl", ReverserInfo.Direction.DESC));
						return PlayerTargetList[0].PlayID;
					}
				}
				return PlayerTargetList[index].PlayID;
			}
			catch (Exception)
			{
				return 0;
			}
		}
	}

	public float FLD_FACE1
	{
		get
		{
			return _FLD_FACE1;
		}
		set
		{
			_FLD_FACE1 = value;
		}
	}

	public float FLD_FACE2
	{
		get
		{
			return _FLD_FACE2;
		}
		set
		{
			_FLD_FACE2 = value;
		}
	}

	public int IsNpc
	{
		get
		{
			return _IsNpc;
		}
		set
		{
			_IsNpc = value;
		}
	}

	public string Name
	{
		get
		{
			return _Name;
		}
		set
		{
			_Name = value;
		}
	}

	public int NPC_SessionID
	{
		get
		{
			return _FLD_INDEX;
		}
		set
		{
			_FLD_INDEX = value;
		}
	}

	public int FLD_PID
	{
		get
		{
			return _FLD_PID;
		}
		set
		{
			_FLD_PID = value;
		}
	}

	public double FLD_AT
	{
		get
		{
			return _FLD_AT;
		}
		set
		{
			_FLD_AT = value;
		}
	}

	public float Rxjh_X
	{
		get
		{
			return _Rxjh_X;
		}
		set
		{
			_Rxjh_X = value;
		}
	}

	public float Rxjh_Y
	{
		get
		{
			return _Rxjh_Y;
		}
		set
		{
			_Rxjh_Y = value;
		}
	}

	public float Rxjh_Z
	{
		get
		{
			return _Rxjh_Z;
		}
		set
		{
			_Rxjh_Z = value;
		}
	}

	public float Rxjh_cs_X
	{
		get
		{
			return _Rxjh_cs_X;
		}
		set
		{
			_Rxjh_cs_X = value;
		}
	}

	public float Rxjh_cs_Y
	{
		get
		{
			return _Rxjh_cs_Y;
		}
		set
		{
			_Rxjh_cs_Y = value;
		}
	}

	public float Rxjh_cs_Z
	{
		get
		{
			return _Rxjh_cs_Z;
		}
		set
		{
			_Rxjh_cs_Z = value;
		}
	}

	public int Rxjh_Map
	{
		get
		{
			return _Rxjh_Map;
		}
		set
		{
			_Rxjh_Map = value;
		}
	}

	public int Rxjh_Exp
	{
		get
		{
			return _Rxjh_Exp;
		}
		set
		{
			_Rxjh_Exp = value;
		}
	}

	public int Max_Rxjh_HP
	{
		get
		{
			return _Max_Rxjh_HP;
		}
		set
		{
			_Max_Rxjh_HP = value;
		}
	}

	public int Rxjh_HP
	{
		get
		{
			return _Rxjh_HP;
		}
		set
		{
			_Rxjh_HP = value;
		}
	}

	public int Level
	{
		get
		{
			return _Level;
		}
		set
		{
			_Level = value;
		}
	}

	public double FLD_DF
	{
		get
		{
			return _FLD_DF;
		}
		set
		{
			_FLD_DF = value;
		}
	}

	public string WorldId
	{
		get
		{
			return _WorldId;
		}
		set
		{
			_WorldId = value;
		}
	}

	public int FLD_AUTO
	{
		get
		{
			return _FLD_AUTO;
		}
		set
		{
			_FLD_AUTO = value;
		}
	}

	public int FLD_FreeDrop { get; set; }

    public double Rxjh_Accuracy { get; set; }

    public double Rxjh_Evasion { get; set; }

	public int FLD_BOSS
	{
		get
		{
			return _FLD_BOSS;
		}
		set
		{
			_FLD_BOSS = value;
		}
	}

	public int FLD_NEWTIME
	{
		get
		{
			return _FLD_NEWTIME;
		}
		set
		{
			_FLD_NEWTIME = value;
		}
	}

	public bool NPCDeath
	{
		get
		{
			return _NPCDeath;
		}
		set
		{
			_NPCDeath = value;
		}
	}

	public bool QuaiXuatHien_DuyNhatMotLan
	{
		get
		{
			return _QuaiXuatHien_DuyNhatMotLan;
		}
		set
		{
			_QuaiXuatHien_DuyNhatMotLan = value;
		}
	}

	public void SetMaxHP(int value) { _Max_Rxjh_HP = value; }

    public void SetHp(int value)
    {
        if(value >= _Max_Rxjh_HP)
            value = _Max_Rxjh_HP;
        _Rxjh_HP = value;
    }
	public NpcClass()
	{
		ID++;
		Ran = new(DateTime.Now.Millisecond);
		PlayList = [];
		double interval = Ran.Next(3000, 15000);
		AutomaticMove = new(interval);
		AutomaticMove.Elapsed += AutomaticMoveEvent;
		AutomaticMove.AutoReset = true;
		AutomaticMove.Enabled = true;
		AutomaticAttack = new(1000.0);
		AutomaticAttack.Elapsed += AutomaticAttackEvent;
		AutomaticAttack.AutoReset = true;
		TrangThai_BatThuong = [];
		TrangThai_MatMau_BatThuong = [];
		_WorldId = World.ServerID.ToString(); // Khởi tạo WorldId với ServerID hiện tại
		ZoneManager.Instance.InitializeNpcDefaultZone(this);
	}

	public void Cw_Add(X_Linh_Thu_Loai SpiritBeast)
	{
		foreach (var playGj in PlayerTargetList)
		{
			if (playGj != null && playGj.PlayID == SpiritBeast.FullServiceID)
			{
				playGj.Gjsl++;
				return;
			}
		}
		PlayerTargetList.Add(new()
		{
			Gjsl = 1,
			PlayID = SpiritBeast.FullServiceID
		});
		PlayCw = SpiritBeast;
	}

	public void PlayList_Add(Players Play)
	{
		if (!Play.Client.TreoMay && !Contains(Play))
		{
			PlayList.Add(Play.SessionID, Play);
		}
	}

	public void PlayList_Remove(Players payer)
	{
		if (Contains(payer))
		{
			PlayList.Remove(payer.SessionID);
		}
	}

	public bool Contains(Players payer)
	{
		if (PlayList != null && PlayList.Count != 0)
		{
			return PlayList.TryGetValue(payer.SessionID, out _);
		}
		return false;
	}

	public void Play_dell(Players payer)
	{
	}

	public PlayGjClass FindMaxDame(List<PlayGjClass> list)
	{
		if (list == null || list.Count == 0)
		{
			// Trả về đối tượng mặc định thay vì ném ngoại lệ
			LogHelper.WriteLine(LogLevel.Error, "FindMaxDame warning: Empty list");
			return new PlayGjClass
			{
				PlayID = 0,
				Gjxl = 0
			};
		}

		// Lọc danh sách các người chơi có TeamID = 0 (không trong team)
		var soloPlayers = list.Where(a => a.TeamID == 0).ToList();

		// Nếu không có người chơi nào solo, trả về đối tượng mặc định
		if (soloPlayers.Count == 0)
		{
			LogHelper.WriteLine(LogLevel.Info, "FindMaxDame: No solo players found");
			return new PlayGjClass
			{
				PlayID = 0,
				Gjxl = 0
			};
		}

		// Nhóm theo PlayID và tính tổng sát thương
		var list2 = (from x in soloPlayers
			group x by x.PlayID into x
			select new
			{
				PlayID = x.Key,
				Gjxl = x.Sum(u => u.Gjxl)
			}).ToList();

		var num = int.MinValue;
		var playID = int.MinValue;

		// Tìm người chơi có sát thương cao nhất
		foreach (var item in list2)
		{
			if (item.Gjxl > num)
			{
				num = item.Gjxl;
				playID = item.PlayID;
			}
		}

        PlayGjClass playGjClass = new()
        {
            PlayID = playID,
            Gjxl = num
        };
        return playGjClass;
	}


	public PlayGjTeamClass FindMaxDameTeam(List<PlayGjClass> list)
	{
		if (list == null || list.Count == 0)
		{
			// Trả về đối tượng mặc định thay vì ném ngoại lệ
			LogHelper.WriteLine(LogLevel.Error, "HandleafterattackNpc warning: Empty list");
			return new PlayGjTeamClass
			{
				Gjxl_team = 0,
				TeamID = 0
			};
		}

		var num = -2147483648.0;
		var teamID = int.MinValue;

		// Lọc danh sách các người chơi có TeamID > 0
		var teamPlayers = list.Where(x => x.TeamID > 0).ToList();

		// Nếu không có người chơi nào trong team, trả về đối tượng mặc định
		if (teamPlayers.Count == 0)
		{
			LogHelper.WriteLine(LogLevel.Info, "HandleafterattackNpc: No team players found");
			return new PlayGjTeamClass
			{
				Gjxl_team = 0,
				TeamID = 0
			};
		}

		// Nhóm theo TeamID và tính tổng sát thương
		var list2 = (from x in teamPlayers
			group x by x.TeamID into g
			select new
			{
				TeamID = g.Key,
				TotalDame = g.Sum(z => z.Gjxl)
			}).ToList();

		// Tìm team có tổng sát thương cao nhất
		foreach (var item in list2)
		{
			if (item.TotalDame > num)
			{
				num = item.TotalDame;
				teamID = item.TeamID;
			}
		}

        PlayGjTeamClass playGjTeamClass = new()
        {
            Gjxl_team = num,
            TeamID = teamID
        };
        return playGjTeamClass;
	}

	public void Play_Add(Players payer, int SoLuongHP)
	{
		var num = 0;
		try
		{
			num = 1;
			if (SoLuongHP < 0 || IsNpc == 1)
			{
				return;
			}
			num = 2;
			using (new Lock(AsyncLocksw, "Play_Add"))
			{
				num = 3;
				var array = PlayerTargetList.ToArray();
				foreach (var playGjClass in array)
				{
					num = 4;
					if (playGjClass == null)
					{
						continue;
					}
					num = 5;
					if (playGjClass.PlayID != payer.SessionID)
					{
						continue;
					}
					num = 6;
					playGjClass.Gjsl++;
					num = 7;
					playGjClass.Gjxl += SoLuongHP;
					num = 8;
					playGjClass.TeamID = payer.TeamID;
					num = 9;
					if (playGjClass.Gjxl >= Max_Rxjh_HP)
					{
						num = 10;
						playGjClass.Gjxl = Max_Rxjh_HP;
					}
					num = 11;
					var array2 = Array.FindAll(World.CauHinhBossTheoDame, s => s.Equals(FLD_PID.ToString()));
					if (!World.CauHinhBossTheoDame.Contains(FLD_PID.ToString()))
					{
						return;
					}
					num = 12;
					var playGjClass2 = FindMaxDame(PlayerTargetList);
					num = 13;
					var characterData = payer.GetCharacterData(playGjClass2.PlayID);
					num = 14;
					var text = "Unknow";
					num = 15;
					var text2 = Rxjh_HP.ToString("N0");
					num = 16;
					if (characterData != null)
					{
						num = 17;
						text = characterData.CharacterName;
					}
					num = 18;
					var text3 = playGjClass2.Gjxl.ToString("N0");
					num = 19;
					var text4 = playGjClass.Gjxl.ToString("N0");
					num = 20;
					var text5 = Math.Round((double)(playGjClass2.Gjxl * 100 / Max_Rxjh_HP), 2) + "%";
					num = 21;
					var text6 = playGjClass.Gjxl.ToString("N0");
					var playGjTeamClass = FindMaxDameTeam(PlayerTargetList);
					num = 22;
					num = 23;
					if (playGjTeamClass.TeamID > 0)
					{
						var text7 = "";
						num = 24;
						if (World.WToDoi.TryGetValue(playGjTeamClass.TeamID, out var value))
						{
							num = 25;
							text7 = value.DoiTruongTen;
						}
						num = 26;
						var text8 = playGjTeamClass.Gjxl_team.ToString("N0");
						num = 28;
						var text9 = Math.Round(playGjTeamClass.Gjxl_team * 100.0 / Max_Rxjh_HP, 2) + "%";
						num = 29;
						// if (playGjClass2.Gjxl > playGjTeamClass.Gjxl_team)
						// {
						// 	num = 30;
						// 	payer.HeThongNhacNho("Sinh lực: (" + text2 + ") Đệ nhất cao thủ: (" + text + ") (" + text5 + ") Tổng cộng: (" + text3 + ") Độc chiếm: (" + text6 + ")");
						// }
						// else
						// {
						// 	num = 31;
						// 	payer.HeThongNhacNho("Sinh lực: (" + text2 + ") Đệ nhất cao thủ: (Đội " + text7 + ") (" + text9 + ") Tổng cộng: (" + text8 + ") Độc chiếm: (" + text6 + ")");
						// }
					}
					else
					{
						num = 32;
						// payer.HeThongNhacNho("Sinh lực: (" + text2 + ") Đệ nhất cao thủ: (" + text + ") (" + text5 + ") Tổng cộng: (" + text3 + ") Độc chiếm: (" + text4 + ")");
					}
					return;
				}
                PlayGjClass playGjClass3 = new()
                {
                    Gjsl = 1,
                    Gjxl = SoLuongHP,
                    TeamID = payer.TeamID
                };
                if (playGjClass3.Gjxl >= Max_Rxjh_HP)
				{
					playGjClass3.Gjxl = Max_Rxjh_HP;
				}
				num = 39;
				playGjClass3.PlayID = payer.SessionID;
				num = 40;
				PlayerTargetList.Add(playGjClass3);
				num = 41;
				if (PlayerTargetList[0].PlayID != payer.SessionID || playGjClass3.Gjxl <= 0)
				{
					return;
				}
				num = 42;
				var array3 = Array.FindAll(World.CauHinhBossTheoDame, s => s.Equals(FLD_PID.ToString()));
				if (World.CauHinhBossTheoDame.Contains(FLD_PID.ToString()))
				{
					num = 43;
					var playGjClass4 = FindMaxDame(PlayerTargetList);
					num = 44;
					var characterData2 = payer.GetCharacterData(playGjClass4.PlayID);
					num = 45;
					var text10 = "Unknow";
					if (characterData2 != null)
					{
						num = 46;
						text10 = characterData2.CharacterName;
					}
					num = 47;
					var text11 = playGjClass4.Gjxl.ToString("N0");
					num = 48;
					var text12 = playGjClass3.Gjxl.ToString("N0");
					num = 49;
					var text13 = Math.Round((double)(playGjClass4.Gjxl * 100 / Max_Rxjh_HP), 2) + "%";
					num = 50;
					var text14 = playGjClass3.Gjxl.ToString("N0");
					num = 59;
					var playGjTeamClass2 = FindMaxDameTeam(PlayerTargetList);
					num = 51;
					num = 52;
					var text15 = Rxjh_HP.ToString("N0");
					num = 54;
					if (playGjTeamClass2.TeamID > 0)
					{
						var text16 = "";
						num = 55;
						if (World.WToDoi.TryGetValue(playGjTeamClass2.TeamID, out var value2))
						{
							num = 56;
							text16 = value2.DoiTruongTen;
						}
						num = 57;
						var text17 = playGjTeamClass2.Gjxl_team.ToString("N0");
						num = 58;
						var text18 = Math.Round(playGjTeamClass2.Gjxl_team * 100.0 / Max_Rxjh_HP, 2) + "%";
						num = 60;
						if (playGjClass4.Gjxl > playGjTeamClass2.Gjxl_team)
						{
							num = 61;
							//payer.HeThongNhacNho("Sinh lực: (" + text15 + ") Đệ nhất cao thủ: (" + text10 + ") (" + text13 + ") Tổng cộng: (" + text11 + ") Độc chiếm: (" + text14 + ")");
						}
						else
						{
							num = 62;
							//payer.HeThongNhacNho("Sinh lực: (" + text15 + ") Đệ nhất cao thủ: (Đội " + text16 + ") (" + text18 + ") Tổng cộng: (" + text17 + ") Độc chiếm: (" + text14 + ")");
						}
					}
					else
					{
						num = 63;
						//payer.HeThongNhacNho("Sinh lực: (" + text15 + ") Đệ nhất cao thủ: (" + text10 + ") (" + text13 + ") Tổng cộng: (" + text11 + ") Độc chiếm: (" + text12 + ")");
					}
				}
				num = 64;
				GuiDi_DiDongSoLieu(payer.NhanVatToaDo_X, payer.NhanVatToaDo_Y, 10, 2);
			}
		}
		catch (Exception ex)
		{
			var text19 = "Loi tai num:[" + num + "][" + payer.Player_Level + "] - PlayGjList:[" + PlayerTargetList[0].PlayID + "] - CharID:[" + payer.SessionID + "] - FLD_BOSS:[" + FLD_BOSS + "] - Level:[" + Level + "] - FLD_AT:[" + FLD_AT + "] - FLD_DF:[" + FLD_DF + "] - MAP:[" + Rxjh_Map + "]";
			// logo.Log_Drop_BOSS_loi(text19, payer.UserName);
			LogHelper.WriteLine(LogLevel.Error, "Lỗi tại Play Add 222 tại num: [" + num + "] + Info:[" + text19 + "] - " + ex.Message);
		}
	}

	public void Play_null()
	{
		if (PlayerTargetList != null && PlayerTargetList.Count > 0)
		{
			PlayerTargetList.Clear();
		}
	}

	public void GetRange_PlayersSendIncreaseQuantityPackage()
	{
		try
		{
			foreach (var value in World.allConnectedChars.Values)
			{
				if (FindPlayers(400, value))
				{
					value.GetReviewScopeNpc();
				}
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Được hàng loạt người chơi gửi Ground Tăng lên Npc Lỗi gói So Lieu： " + ex);
		}
	}

	public void GetRange_PlayersSendIncreaseQuantityPackage_Event_DuongDua_F1()
	{
		try
		{
			foreach (var value in World.allConnectedChars.Values)
			{
				if (FindPlayers(800, value))
				{
					value.GetReviewScopeNpc();
				}
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Được hàng loạt người chơi gửi Ground Tăng lên Npc Lỗi gói So Lieu： " + ex);
		}
	}
	 public void InitiateDisposeTime()
    {
        LogHelper.WriteLine(LogLevel.Error, "Initial Dispose Time " + DisPoseTime);
        if(DisPoseTime > 0)
        {
            AutomaticDisPose = new System.Timers.Timer(DisPoseTime)
            {
                Enabled = true
            };
            AutomaticDisPose.Elapsed += AutomaticDisposeEvent;
            AutomaticDisPose.AutoReset = false;
        }
    }

    private void AutomaticDisposeEvent(object sender, ElapsedEventArgs e)
    {
        LogHelper.WriteLine(LogLevel.Info, "Tự động xóa NPC");
        //GuiDuLieu_TuVong_MotLanCuaQuaiVat();
        GuiDiTuVongSoLieuWrapper();
    }

	public void Dispose()
	{
		int num = 0;
		try
		{
			CurrentZone?.NPCs.Remove(this);
            CurrentZone = null;
			BossStage = 0;
			MapClass.delnpc(Rxjh_Map, NPC_SessionID);
			if (AutomaticAttack != null)
			{
				AutomaticAttack.Enabled = false;
				AutomaticAttack.Close();
				AutomaticAttack.Dispose();
			}
			if (AutomaticMove != null)
			{
				AutomaticMove.Enabled = false;
				AutomaticMove.Close();
				AutomaticMove.Dispose();
			}
			if (TuDongHoiSinh != null)
			{
				TuDongHoiSinh.Enabled = false;
				TuDongHoiSinh.Close();
				TuDongHoiSinh.Dispose();
				TuDongHoiSinh = null;
			}
			if(AutomaticDisPose != null)
            {
                AutomaticDisPose.Close();
                AutomaticDisPose.Dispose();
                AutomaticDisPose = null;
            }
			num = 1;
			Play_null();
			if (PlayCw != null)
			{
				PlayCw = null;
			}
			num = 2;
			GetRange_Players_GuiDiBienMatSoLieu_Package();
			tem?.Clear();
			templayer?.Clear();
			num = 3;
			if (PlayList != null)
			{
				PlayList.Dispose();
				PlayList = null;
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, $"NPC Khép kín So Lieu Dispose()  {num}  error：" + ex);
		}
	}

	public void GetRange_Players_GuiDiBienMatSoLieu_Package()
	{
		try
		{
			if (PlayList != null)
			{
				foreach (var value in PlayList.Values)
				{
					if (value.Client != null)
					{
						value.GetReviewScopeNpc();
					}
				}
			}
			PlayList?.Clear();
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "NPC Lấy phạm vi người chơi gửi đi [biến mất] số liệu [gói 3] lỗi： " + ex);
		}
	}

	~NpcClass()
	{
	}

	public bool ContainsKeyInAbnormalState(int Key)
	{
		X_Di_Thuong_Trang_Thai_Loai value;
		if (TrangThai_BatThuong != null && TrangThai_BatThuong.Count != 0)
		{
			return TrangThai_BatThuong.TryGetValue(Key, out value);
		}
		return false;
	}

	public void getbl()
	{
		if (PlayList.Count > 0)
		{
			LogHelper.WriteLine(LogLevel.Info, _Name + " nhân vâ\u0323t 1：" + PlayList.Count);
			if (AutomaticMove != null)
			{
				LogHelper.WriteLine(LogLevel.Info, _Name + " Nhan Vat Automatic Move：" + AutomaticMove.Enabled);
			}
			if (AutomaticAttack != null)
			{
				LogHelper.WriteLine(LogLevel.Info, _Name + " Nhan Vat Automatic Attack：" + AutomaticAttack.Enabled);
			}
			if (TuDongHoiSinh != null)
			{
				LogHelper.WriteLine(LogLevel.Info, _Name + " Nhan Vat Tu Dong Hoi Sinh：" + TuDongHoiSinh.Enabled);
			}
		}
		if (PlayerTargetList.Count > 0)
		{
			LogHelper.WriteLine(LogLevel.Info, _Name + " Cong Kich 123：" + PlayerTargetList.Count);
		}
		if (PlayCw != null)
		{
			LogHelper.WriteLine(LogLevel.Info, _Name + " Spirit Beast：" + PlayCw.Name + "Tên chủ sở hữu：" + PlayCw.ZrName);
		}
	}

	public int ThuDuocKinhNghiem()
	{
		try
		{
			var num = Rxjh_Exp * World.KinhNghiem_BoiSo;
			if (World.Gioi_han_EXP_khi_co_TLC == 1 && ThoiGian_KetThuc_Rate_Exp_Drop_Gold())
			{
				_ = Rxjh_Exp;
				num = 0;
			}
			else
			{
				num = Rxjh_Exp * World.KinhNghiem_BoiSo;
			}
			if (num < 0)
			{
				num = 0;
				var minValue = num / World.Random_Rate_KinhNghiem;
				return new Random(DateTime.Now.Millisecond).Next(minValue, num);
			}
			var num2 = num / World.Random_Rate_KinhNghiem;
			var num3 = 0;
			if (num + num2 > 2147483000)
			{
				num3 = 2147483000;
			}
			else
			{
				num3 = num + num2;
				if (num3 < 0)
				{
					num3 = 0;
				}
			}
			var num4 = new Random(DateTime.Now.Millisecond).Next(num - num2, num3);
			if (num4 < 0)
			{
				num = 0;
				var minValue2 = num / World.Random_Rate_KinhNghiem;
				return new Random(DateTime.Now.Millisecond).Next(minValue2, num);
			}
			return num4;
		}
		catch
		{
			return 0;
		}
	}

	public int ThuDuocTien(Players play)
	{
		try
		{
			double num = Rxjh_Exp * World.Tien_BoiSo / World.Phan_Tram_Chia_Drop_Gold;
			if (World.Gioi_han_EXP_khi_co_TLC == 1 && ThoiGian_KetThuc_Rate_Exp_Drop_Gold())
			{
				_ = Rxjh_Exp;
				num = 0.0;
			}
			else if (Level < 155)
			{
				if (play.Player_Level < 100)
				{
					num = Rxjh_Exp * World.Tien_BoiSo / World.Phan_Tram_Chia_Drop_Gold;
				}
				else
				{
					var num2 = play.Player_Level - Level;
					if (play.Player_Level > Level)
					{
						num /= 0.5 + num2 + 2.0;
					}
					else if (play.Player_Level == Level)
					{
						num /= 2.0;
					}
					else
					{
						var num3 = Level - play.Player_Level;
						if (num3 == 1)
						{
							num = Rxjh_Exp * World.Tien_BoiSo / World.Phan_Tram_Chia_Drop_Gold / World.Gold_Drop_Cach_1_Level_Quai;
						}
						else if (num3 == 2)
						{
							num = Rxjh_Exp * World.Tien_BoiSo / World.Phan_Tram_Chia_Drop_Gold / World.Gold_Drop_Cach_2_Level_Quai;
						}
						else if (num3 == 3)
						{
							num = Rxjh_Exp * World.Tien_BoiSo / World.Phan_Tram_Chia_Drop_Gold / World.Gold_Drop_Cach_3_Level_Quai;
						}
						else if (num3 == 4)
						{
							num = Rxjh_Exp * World.Tien_BoiSo / World.Phan_Tram_Chia_Drop_Gold / World.Gold_Drop_Cach_4_Level_Quai;
						}
						else if (num3 == 5)
						{
							num = Rxjh_Exp * World.Tien_BoiSo / World.Phan_Tram_Chia_Drop_Gold / World.Gold_Drop_Cach_5_Level_Quai;
						}
						else if (num3 == 6)
						{
							num = Rxjh_Exp * World.Tien_BoiSo / World.Phan_Tram_Chia_Drop_Gold / World.Gold_Drop_Cach_6_Level_Quai;
						}
						else if (num3 == 7)
						{
							num = Rxjh_Exp * World.Tien_BoiSo / World.Phan_Tram_Chia_Drop_Gold / World.Gold_Drop_Cach_7_Level_Quai;
						}
						else if (num3 == 8)
						{
							num = Rxjh_Exp * World.Tien_BoiSo / World.Phan_Tram_Chia_Drop_Gold / World.Gold_Drop_Cach_8_Level_Quai;
						}
						else if (num3 == 9)
						{
							num = Rxjh_Exp * World.Tien_BoiSo / World.Phan_Tram_Chia_Drop_Gold / World.Gold_Drop_Cach_9_Level_Quai;
						}
						else if (num3 >= 10 && num3 <= 14)
						{
							num = Rxjh_Exp * World.Tien_BoiSo / World.Phan_Tram_Chia_Drop_Gold / World.Gold_Drop_Cach_10_Level_Quai;
						}
						else if (num3 >= 15 && num3 <= 19)
						{
							num = Rxjh_Exp * World.Tien_BoiSo / World.Phan_Tram_Chia_Drop_Gold / World.Gold_Drop_Cach_15_Level_Quai;
						}
						else if (num3 == 20)
						{
							num = Rxjh_Exp * World.Tien_BoiSo / World.Phan_Tram_Chia_Drop_Gold / World.Gold_Drop_Cach_20_Level_Quai;
						}
					}
					if (play.Player_Level >= 100 && play.Player_Level < 115)
					{
						num /= World.Giam_Gold_Drop_Level_100_114;
					}
					else if (play.Player_Level >= 115 && play.Player_Level < 120)
					{
						num /= World.Giam_Gold_Drop_Level_115_119;
					}
					else if (play.Player_Level >= 120 && play.Player_Level < 125)
					{
						num /= World.Giam_Gold_Drop_Level_120_124;
					}
					else if (play.Player_Level >= 125 && play.Player_Level < 129)
					{
						num /= World.Giam_Gold_Drop_Level_125_128;
					}
					else if (play.Player_Level >= 129 && play.Player_Level < 131)
					{
						num /= World.Giam_Gold_Drop_Level_129_130;
					}
				}
			}
			else if (Level >= 155 && Level < 170)
			{
				var num4 = play.Player_Level - Level;
				if (play.Player_Level > Level)
				{
					num /= 0.5 + num4 + 3.0;
				}
				else if (play.Player_Level == Level)
				{
					num /= 2.0;
				}
				else
				{
					switch (Level - play.Player_Level)
					{
					case 1:
						num = Rxjh_Exp * World.Tien_BoiSo / World.Phan_Tram_Chia_Drop_Gold / World.Gold_Drop_Cach_1_Level_Quai * World.Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_1;
						break;
					case 2:
						num = Rxjh_Exp * World.Tien_BoiSo / World.Phan_Tram_Chia_Drop_Gold / World.Gold_Drop_Cach_2_Level_Quai * World.Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_2;
						break;
					case 3:
						num = Rxjh_Exp * World.Tien_BoiSo / World.Phan_Tram_Chia_Drop_Gold / World.Gold_Drop_Cach_3_Level_Quai * World.Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_3;
						break;
					case 4:
						num = Rxjh_Exp * World.Tien_BoiSo / World.Phan_Tram_Chia_Drop_Gold / World.Gold_Drop_Cach_4_Level_Quai * World.Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_4;
						break;
					case 5:
						num = Rxjh_Exp * World.Tien_BoiSo / World.Phan_Tram_Chia_Drop_Gold / World.Gold_Drop_Cach_5_Level_Quai * World.Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_5;
						break;
					case 6:
						num = Rxjh_Exp * World.Tien_BoiSo / World.Phan_Tram_Chia_Drop_Gold / World.Gold_Drop_Cach_5_Level_Quai * World.Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_6;
						break;
					case 7:
						num = Rxjh_Exp * World.Tien_BoiSo / World.Phan_Tram_Chia_Drop_Gold / World.Gold_Drop_Cach_5_Level_Quai * World.Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_7;
						break;
					case 8:
						num = Rxjh_Exp * World.Tien_BoiSo / World.Phan_Tram_Chia_Drop_Gold / World.Gold_Drop_Cach_5_Level_Quai * World.Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_8;
						break;
					case 9:
						num = Rxjh_Exp * World.Tien_BoiSo / World.Phan_Tram_Chia_Drop_Gold / World.Gold_Drop_Cach_5_Level_Quai * World.Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_9;
						break;
					case 10:
						num = Rxjh_Exp * World.Tien_BoiSo / World.Phan_Tram_Chia_Drop_Gold / World.Gold_Drop_Cach_5_Level_Quai * World.Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_10;
						break;
					case 11:
						num = Rxjh_Exp * World.Tien_BoiSo / World.Phan_Tram_Chia_Drop_Gold / World.Gold_Drop_Cach_5_Level_Quai * World.Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_11;
						break;
					case 12:
						num = Rxjh_Exp * World.Tien_BoiSo / World.Phan_Tram_Chia_Drop_Gold / World.Gold_Drop_Cach_5_Level_Quai * World.Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_12;
						break;
					case 13:
						num = Rxjh_Exp * World.Tien_BoiSo / World.Phan_Tram_Chia_Drop_Gold / World.Gold_Drop_Cach_5_Level_Quai * World.Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_13;
						break;
					case 14:
						num = Rxjh_Exp * World.Tien_BoiSo / World.Phan_Tram_Chia_Drop_Gold / World.Gold_Drop_Cach_5_Level_Quai * World.Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_14;
						break;
					case 15:
						num = Rxjh_Exp * World.Tien_BoiSo / World.Phan_Tram_Chia_Drop_Gold / World.Gold_Drop_Cach_5_Level_Quai * World.Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_15;
						break;
					case 16:
						num = Rxjh_Exp * World.Tien_BoiSo / World.Phan_Tram_Chia_Drop_Gold / World.Gold_Drop_Cach_5_Level_Quai * World.Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_16;
						break;
					case 17:
						num = Rxjh_Exp * World.Tien_BoiSo / World.Phan_Tram_Chia_Drop_Gold / World.Gold_Drop_Cach_5_Level_Quai * World.Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_17;
						break;
					case 18:
						num = Rxjh_Exp * World.Tien_BoiSo / World.Phan_Tram_Chia_Drop_Gold / World.Gold_Drop_Cach_5_Level_Quai * World.Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_18;
						break;
					case 19:
						num = Rxjh_Exp * World.Tien_BoiSo / World.Phan_Tram_Chia_Drop_Gold / World.Gold_Drop_Cach_5_Level_Quai * World.Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_19;
						break;
					case 20:
						num = Rxjh_Exp * World.Tien_BoiSo / World.Phan_Tram_Chia_Drop_Gold / World.Gold_Drop_Cach_5_Level_Quai * World.Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_20;
						break;
					default:
						num = 0.0;
						return 0;
					}
				}
			}
			else if (Level >= 170)
			{
				num = Rxjh_Exp * World.Tien_BoiSo / World.Phan_Tram_Chia_Drop_Gold;
			}
			if (num < 0.0)
			{
				num = 0.0;
				var minValue = (int)num / World.Random_Rate_Gold;
				return new Random(DateTime.Now.Millisecond).Next(minValue, (int)num);
			}
			var num5 = (int)num / World.Random_Rate_Gold;
			var num6 = 0;
			if (num + num5 > 2147483000.0)
			{
				num6 = 2147483000;
			}
			else
			{
				num6 = (int)num + num5;
				if (num6 < 0)
				{
					num6 = 0;
				}
			}
			var num7 = new Random(DateTime.Now.Millisecond).Next((int)num - num5, num6);
			if (num7 < 0)
			{
				num = 0.0;
				var minValue2 = (int)num / World.Random_Rate_Gold;
				return new Random(DateTime.Now.Millisecond).Next(minValue2, (int)num);
			}
			return num7;
		}
		catch
		{
			return 0;
		}
	}

	public int ThuDuocLichLuyen(Players play)
	{
		try
		{
			var num = Rxjh_Exp * World.LichLuyenBoiSo / Level / World.Phan_Tram_Chia_Ky_Nang;
			if (World.Gioi_han_EXP_khi_co_TLC == 1 && ThoiGian_KetThuc_Rate_Exp_Drop_Gold())
			{
				_ = Rxjh_Exp;
				num = 0;
			}
			else
			{
				num = Rxjh_Exp * World.LichLuyenBoiSo / Level / World.Phan_Tram_Chia_Ky_Nang;
			}
			if (num < 0)
			{
				num = 0;
				var minValue = num / World.Phan_Tram_Chia_Ky_Nang;
				return new Random(DateTime.Now.Millisecond).Next(minValue, num);
			}
			var num2 = num / World.Phan_Tram_Chia_Ky_Nang;
			var num3 = 0;
			if (num + num2 > 2147483000)
			{
				num3 = 2147483000;
			}
			else
			{
				num3 = num + num2;
				if (num3 < 0)
				{
					num3 = 0;
				}
			}
			var num4 = (int)(new Random().Next(num - num2, num3) * (1.0 - World.PhanTram_GiamXuong_TienNhanDuoc));
			if (num4 < 0)
			{
				num = 0;
				var minValue2 = num / World.Phan_Tram_Chia_Ky_Nang;
				return new Random(DateTime.Now.Millisecond).Next(minValue2, num);
			}
			return num4;
		}
		catch
		{
			return 0;
		}
	}

	public int ThuDuocThangThienLichLuyen(Players playe)
	{
		try
		{
			if (World.Gioi_han_EXP_khi_co_TLC == 1 && ThoiGian_KetThuc_Rate_Exp_Drop_Gold())
			{
				return 0;
			}
			var num = (int)(Rxjh_Exp * World.ThangThien_LichLuyen_BoiSo / 1000.0 / World.Phan_Tram_Chia_Ky_Nang_TT);
			if (playe.Player_Level < 150)
			{
				num *= World.Random_Rate_KyNangThangThien;
			}
			num = Math.Max(0, num);
			var num2 = num / World.Random_Rate_KyNangThangThien;
			var maxValue = Math.Min(2147483000, num + num2);
			Random random = new(DateTime.Now.Millisecond);
			var val = random.Next(num - num2, maxValue);
			return Math.Max(0, val);
		}
		catch
		{
			return 0;
		}
	}

	private bool ThoiGian_KetThuc_Rate_Exp_Drop_Gold()
	{
		var now = DateTime.Now;
		return now.Hour == World.TheLucChien_MoRa_Gio && FLD_BOSS == 0 && now.Minute < World.Thoi_Gian_Ket_Thuc_Giam_Kinh_Nghiem_nho_hon_hoac_bang;
	}

	private void AutomaticMoveEvent(object sender, ElapsedEventArgs e)
	{
		if (CurrentZone != null && CurrentZone.IsCrossServer && CurrentZone.OriginServerID != World.ServerID.ToString())
		{
			// Cross Server
			// Không xử lý tự động di chuyển nếu là boss clone
			return;
		}
		var num = 0;
		if (World.jlMsg == 1)
		{
			LogHelper.WriteLine(0, "Automatic Move Event");
		}
		try
		{
			if (FLD_PID == 16270 || FLD_PID == 16271 || FLD_PID == 15293)
			{
				AutomaticMove.Enabled = false;
			}
			else if (IsNpc != 0)
			{
				AutomaticMove.Enabled = false;
			}
			else
			{
				if (PlayList.Count < 1 || NPCDeath)
				{
					return;
				}
				if (IsNpc != 1 && FLD_AT > 0.0)
				{
					num = 3;
					AutomaticMove.Interval = new Random(DateTime.Now.Millisecond).Next(3000, 10000);
					if (FLD_AUTO == 1 && GetRangePlayers())
					{
						num = 4;
						AutomaticMove.Enabled = false;
						AutomaticAttack.Enabled = true;
					}
					else if (FLD_PID != 16431 && FLD_PID != 16430 && FLD_PID != 16435)
					{
						num = 5;
						GetRangePlayers();
						GuiDi_DiDongSoLieu(Rxjh_cs_X, Rxjh_cs_Y, (FLD_PID != 5) ? 50 : 20, 1);
					}
					else
					{
						num = 6;
						GetRangePlayers();
						AutomaticMove.Enabled = false;
						AutomaticAttack.Enabled = false;
					}
					return;
				}
				AutomaticMove.Interval = 20000.0;
				foreach (var value in PlayList.Values)
				{
					if (value.Client != null)
					{
						num = 8;
						if (!World.allConnectedChars.ContainsKey(value.Client.SessionID))
						{
							num = 9;
							tem.Add(value);
						}
					}
					else
					{
						num = 10;
						tem.Add(value);
					}
				}
				foreach (Players item in tem)
				{
					item.Client?.Dispose();
					PlayList.Remove(item.SessionID);
				}
				tem.Clear();
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Lỗi Event di chuyển tự động tại num：[" + num + "] - " + ex);
		}
	}

	private void TuDongHoiSinhEvent(object sender, ElapsedEventArgs e)
	{
		try
		{
			if (IsNpc == 1)
			{
				TuDongHoiSinh.Enabled = false;
				return;
			}
			AutomaticMove.Enabled = true;
			if (!NPCDeath)
			{
				return;
			}
			DoiMoi_NPC_HoiSinh_SoLieu();
			if (FLD_BOSS == 1)
			{
				foreach (var value in World.allConnectedChars.Values)
				{
					if (!value.Client.TreoMay)
					{
                        var text = "Ma tôn [" + Name + "] tái xuất giang hồ, hồi sinh đầy uy lực tại [Kênh " + World.ServerID + "], ẩn mình nơi hiểm địa [" + X_Toa_Do_Class.getmapname(Rxjh_Map) + "] ở tọa độ [" + Rxjh_X + "," + Rxjh_Y + "]. Anh hùng thiên hạ, ai dám đến diệt trừ yêu ma?";
						World.conn.Transmit("PK_MESSAGE|" + 22 + "|" + text);
					}
				}
				return;
			}
			if (FLD_PID != 16278 || Rxjh_Map != 40101 || World.DCH_Progress == 0)
			{
				return;
			}
			foreach (var value2 in World.allConnectedChars.Values)
			{
				if (value2.NhanVatToaDo_BanDo == 40101)
				{
					value2.HeThongNhacNho("Đại ma đầu Diêm La Quân đã tái sinh, quần hùng chuẩn bị nghênh chiến!!!", 7, "Thiên cơ các");
					value2.HeThongNhacNho("Đại ma đầu Diêm La Quân đã tái sinh, giang hồ rung chuyển!!!", 6, "Thiên cơ các");
					value2.HeThongNhacNho("Đại ma đầu Diêm La Quân đã tái sinh, anh hùng tứ phương hội tụ!!!", 10, "Thiên cơ các");
				}
			}
		}
		catch (Exception)
		{
			if (TuDongHoiSinh != null)
			{
				TuDongHoiSinh.Enabled = false;
				TuDongHoiSinh.Close();
				TuDongHoiSinh.Dispose();
				TuDongHoiSinh = null;
			}
		}
		finally
		{
			if (TuDongHoiSinh != null)
			{
				TuDongHoiSinh.Enabled = false;
				TuDongHoiSinh.Close();
				TuDongHoiSinh.Dispose();
				TuDongHoiSinh = null;
			}
		}
	}

		public void NPC_Attack()
	{
		try
		{
			if (FLD_PID == 16270 || FLD_PID == 16271 || FLD_PID == 15293 || FLD_PID == 15732)
			{
				AutomaticAttack.Enabled = false;
				if (FLD_PID == 15732)
				{
					 AutomaticMove.Enabled = false;
				}
			}
			else if (IsNpc != 0)
			{
				AutomaticAttack.Enabled = false;
			}
			else if (Rxjh_HP < 0)
			{
				AutomaticAttack.Enabled = false;
			}
			else
			{
				if (FLD_AT <= 0.0)
				{
					return;
				}

				if (PlayList != null && PlayList.TryGetValue(PlayerWid, out var targetPlayer))
                {
                    attackCount++;
					if (FLD_BOSS == 1)
					{
						HandleBossMagicAttack(targetPlayer,8);
					}
					else{
                    	HandleNpcAttackPlayer(targetPlayer);
					}
                }
                else
				{
					PlayCw = null;
					var array8 = Array.FindAll(World.CauHinhBossTheoDame, s => s.Equals(FLD_PID.ToString()));
					if (!World.CauHinhBossTheoDame.Contains(FLD_PID.ToString()))
					{
						Play_null();
					}
					AutomaticAttack.Enabled = false;
					AutomaticMove.Enabled = true;
					Rxjh_X = Rxjh_cs_X;
					Rxjh_Y = Rxjh_cs_Y;
					Rxjh_Z = Rxjh_cs_Z;
					GuiDi_DiDongSoLieu(Rxjh_cs_X, Rxjh_cs_Y, 50, 1);
				}
				if (DiTinh_MissDame)
				{
				}
			}
		}
		catch (Exception)
		{
			PlayCw = null;
			var array9 = Array.FindAll(World.CauHinhBossTheoDame, s => s.Equals(FLD_PID.ToString()));
			if (!World.CauHinhBossTheoDame.Contains(FLD_PID.ToString()))
			{
				Play_null();
			}
			DiTinh_MissDame = false;
			AutomaticAttack.Enabled = false;
			AutomaticMove.Enabled = true;
			Rxjh_X = Rxjh_cs_X;
			Rxjh_Y = Rxjh_cs_Y;
			Rxjh_Z = Rxjh_cs_Z;
			GuiDi_DiDongSoLieu(Rxjh_cs_X, Rxjh_cs_Y, 50, 1);
		}
	}

	private void AutomaticAttackEvent(object sender, ElapsedEventArgs e)
	{
		// if (CurrentZone != null && CurrentZone.IsCrossServer && CurrentZone.OriginServerID != World.ServerID.ToString())
		// {
		// 	// Cross Server
		// 	// Không xử lý tấn công nếu là boss clone
		// 	return;
		// }
        NPC_Attack();
	}

	private void HandleNpcAttackPlayer(Players targetPlayer)
	{
		var num = (int)(FLD_AT * 0.8);
		Random random = new(DateTime.Now.Millisecond);
		AutomaticAttack.Interval = 1500.0;
		var attackDamage = random.Next(num - 8, num + 8);
        var num3 = 0;
        num3 = ((targetPlayer.NhanVatToaDo_BanDo != 40101) ? 250 : 200);

		if (targetPlayer.NhanVat_HP > 0 && !targetPlayer.PlayerTuVong)
		{
			var targetDef = targetPlayer.FLD_NhanVatCoBan_PhongNgu + targetPlayer.FLD_NhanVat_ThemVao_PhongThu_QuaiVat + targetPlayer.ThangThien_5_MaHonChiLuc;
			if (!targetPlayer.KiemTraDocXaXuatDongTrangThai() && targetPlayer.Player_Job == 12)
			{
				targetPlayer.TriggerAttributePromotion = 0;
				if (targetPlayer.KhongGiPhaNoi >= RNG.Next(1, 100))
				{
					targetPlayer.TriggerAttributePromotion = 1;
					targetPlayer.ShowBigPrint(targetPlayer.SessionID, 1010);
				}
			}
			if (targetPlayer.FLD_TrangBi_ThemVao_GiamXuongTiLePhanTramCongKich > 0.0)
			{
				attackDamage = (int)(attackDamage * (1.0 - targetPlayer.FLD_TrangBi_ThemVao_GiamXuongTiLePhanTramCongKich));
			}
			var attackType = 28;
			if (Level >= 60)
			{
				switch (RNG.Next(0, 10))
				{
					case 1:
						attackType = 28;
						break;
					case 2:
						attackType = 29;
						break;
					case 4:
						attackType = 29;
						break;
				}
				if (attackType == 29)
				{
					attackDamage = (int)(attackDamage * 1.05);
				}
			}
			if (targetPlayer.TrungCapPhuHon_DiTinh != 0 && RNG.Next(1, 200) <= (double)targetPlayer.TrungCapPhuHon_DiTinh)
			{
				targetPlayer.ShowBigPrint(targetPlayer.SessionID, 405);
				X_Them_Vao_Trang_Thai_Loai value2 = new(targetPlayer, 3000, 1000000954, 0);
				targetPlayer.AppendStatusList.Add(1000000954, value2);
				targetPlayer.StatusEffect(BitConverter.GetBytes(1000000954), 1, 3000);
				attackDamage = 0;
			}
			if (targetPlayer.Player_Job == 3)
			{
				if (RNG.Next(1, 100) <= targetPlayer.THUONG_ChuyenCongViThu + targetPlayer.THUONG_ThangThien_2_KhiCong_DiThoiViTien)
				{
					targetPlayer.ShowBigPrint(targetPlayer.SessionID, 130);
					targetDef += targetPlayer.FLD_CongKich * 0.2;
				}
			}
			else if (targetPlayer.Player_Job == 10)
			{
				if (RNG.Next(0, 100) <= targetPlayer.QuyenSu_ChuyenCongViThu)
				{
					targetPlayer.ShowBigPrint(targetPlayer.SessionID, 130);
					targetDef += targetPlayer.FLD_CongKich;
				}
				if (attackType == 29 && RNG.Next(1, 110) <= targetPlayer.QuyenSu_KimCuongBatHoai)
				{
					targetPlayer.ShowBigPrint(targetPlayer.SessionID, 554);
					attackDamage = (int)(attackDamage * 0.1);
				}
				if (RNG.Next(1, 100) <= targetPlayer.ThangThien_5_BatTu_ChiKhu)
				{
					targetPlayer.ShowBigPrint(targetPlayer.SessionID, 1021);
					attackDamage = 0;
				}
			}
			else if (targetPlayer.Player_Job == 12 && RNG.Next(0, 100) <= targetPlayer.TuHao_ChuyenCongViThu)
			{
				targetPlayer.ShowBigPrint(targetPlayer.SessionID, 130);
				targetDef += targetPlayer.FLD_CongKich / 2.0;
			}
			if (targetPlayer.TrungCapPhuHon_HoThe != 0 && RNG.Next(1, 100) <= targetPlayer.TrungCapPhuHon_HoThe)
			{
				targetPlayer.ShowBigPrint(targetPlayer.SessionID, 406);
				targetPlayer.NhanVat_HP += attackDamage;
				targetPlayer.CapNhat_HP_MP_SP();
				attackDamage = 0;
			}
			var finalDamage = ((!(attackDamage > targetDef)) ? 1 : (attackDamage - (int)targetDef));
			if (FindPlayers(20, targetPlayer))
            {
                if (targetPlayer.TrungCapPhuHon_HonNguyen != 0 && RNG.Next(1, 150) <= targetPlayer.TrungCapPhuHon_HonNguyen + targetPlayer.TrungCapPhuHon_HonNguyen_Giap)
                {
                    targetPlayer.ShowBigPrint(targetPlayer.SessionID, 407);
                    finalDamage = (int)(finalDamage * 0.5);
                }
                if (targetPlayer.FLD_TrangBi_GiamXuong_MucThuongTon > 0.0)
                {
                    finalDamage -= (int)targetPlayer.FLD_TrangBi_GiamXuong_MucThuongTon;
                }
                var num14 = 0;
                if (targetPlayer.Player_Job == 1)
                {
                    var num15 = targetPlayer.FLD_NhanVatCoBan_NeTranh * 0.01;
                    if (RNG.Next(1, 100) <= num15)
                    {
                        num14 = 1;
                        finalDamage = 0;
                    }
                }
                else if (targetPlayer.Player_Job == 2)
                {
                    double num16 = finalDamage;
                    if (RNG.Next(1, 105) <= targetPlayer.KIEM_ThangThien_1_KhiCong_HoThan_CuongKhi)
                    {
                        targetPlayer.ShowBigPrint(targetPlayer.SessionID, 25);
                        finalDamage = (int)(num16 * 0.5);
                    }
                    var num17 = targetPlayer.KIEM_HoiLieu_ThanPhap + targetPlayer.FLD_NhanVatCoBan_NeTranh * 0.01;
                    if (RNG.Next(1, 100) <= num17)
                    {
                        num14 = 1;
                        finalDamage = 0;
                    }
                }
                else if (targetPlayer.Player_Job == 3)
                {
                    var num18 = targetPlayer.FLD_NhanVatCoBan_NeTranh * 0.01;
                    if (RNG.Next(1, 100) <= num18)
                    {
                        num14 = 1;
                        finalDamage = 0;
                    }
                }
                else if (targetPlayer.Player_Job == 4)
                {
                    var num19 = targetPlayer.FLD_NhanVatCoBan_NeTranh * 0.01;
                    if (RNG.Next(1, 100) <= num19)
                    {
                        num14 = 1;
                        finalDamage = 0;
                    }
                }
                else if (targetPlayer.Player_Job == 5)
                {
                    var num20 = targetPlayer.FLD_NhanVatCoBan_NeTranh * 0.01;
                    if (RNG.Next(1, 100) <= num20)
                    {
                        num14 = 1;
                        finalDamage = 0;
                    }
                }
                else if (targetPlayer.Player_Job == 6)
                {
                    if (RNG.Next(1, 110) <= targetPlayer.NINJA_ThangThien_1_KhiCong_DaMaTrienThan)
                    {
                        finalDamage = (int)(finalDamage * 0.7);
                        targetPlayer.ShowBigPrint(targetPlayer.SessionID, 370);
                    }
                    if (RNG.Next(1, 100) <= targetPlayer.NINJA_ThangThien_2_KhiCong_ThuanThuyThoiChu)
                    {
                        targetPlayer.AddBlood((int)(finalDamage * 0.2));
                        targetPlayer.ShowBigPrint(targetPlayer.SessionID, 371);
                    }
                    var num21 = targetPlayer.NINJA_TamHoaTuDinh + targetPlayer.FLD_NhanVatCoBan_NeTranh * 0.02;
                    if (RNG.Next(1, 110) <= num21)
                    {
                        num14 = 1;
                        targetPlayer.NINJA_LienTieuDaiDa_SoLuong = finalDamage * targetPlayer.NINJA_LienTieuDaiDa;
                        finalDamage = 0;
                    }
                }
                else if (targetPlayer.Player_Job == 7)
                {
                    var num22 = targetPlayer.FLD_NhanVatCoBan_NeTranh * 0.01;
                    if (RNG.Next(1, 100) <= num22)
                    {
                        num14 = 1;
                        finalDamage = 0;
                    }
                }
                else if (targetPlayer.Player_Job == 8)
                {
                    var num23 = targetPlayer.FLD_NhanVatCoBan_NeTranh * 0.01;
                    if (RNG.Next(1, 100) <= num23)
                    {
                        num14 = 1;
                        finalDamage = 0;
                    }
                }
                else if (targetPlayer.Player_Job == 9)
                {
                    double num24 = finalDamage;
                    if (RNG.Next(1, 100) <= targetPlayer.DamHoaLien_HoThan_CuongKhi)
                    {
                        targetPlayer.ShowBigPrint(targetPlayer.SessionID, 25);
                        finalDamage = (int)(num24 * 0.5);
                    }
                    var num25 = targetPlayer.DamHoaLien_HoiLieu_ThanPhap + targetPlayer.FLD_NhanVatCoBan_NeTranh * 0.01;
                    if (RNG.Next(1, 120) <= num25)
                    {
                        num14 = 1;
                        finalDamage = 0;
                    }
                }
                else if (targetPlayer.Player_Job == 10)
                {
                    var num26 = targetPlayer.FLD_NhanVatCoBan_NeTranh * 0.01;
                    if (RNG.Next(1, 100) <= num26)
                    {
                        num14 = 1;
                        finalDamage = 0;
                    }
                }
                else if (targetPlayer.Player_Job == 11)
                {
                    var num27 = targetPlayer.FLD_NhanVatCoBan_NeTranh * 0.01;
                    if (RNG.Next(1, 100) <= num27)
                    {
                        num14 = 1;
                        finalDamage = 0;
                    }
                }
                else if (targetPlayer.Player_Job == 12)
                {
                    var num28 = targetPlayer.FLD_NhanVatCoBan_NeTranh * 0.01;
                    if (RNG.Next(1, 100) <= num28)
                    {
                        num14 = 1;
                        finalDamage = 0;
                    }
                }
                else if (targetPlayer.Player_Job == 13)
                {
                    var num29 = targetPlayer.FLD_NhanVatCoBan_NeTranh * 0.01;
                    if (RNG.Next(1, 100) <= num29)
                    {
                        num14 = 1;
                        finalDamage = 0;
                    }
                }
                if (targetPlayer.NhanVat_BatTu == 1)
                {
                    num14 = 1;
                    finalDamage = 0;
                }
                if (finalDamage <= 4 && num14 == 0)
                {
                    finalDamage = RNG.Next(5, 10);
                }
                var shield = 0;
                if (targetPlayer.Player_Job == 11 && targetPlayer.MaiLieuChan_ChuongLucKichHoat > 0.0)
                {
                    shield = (int)(finalDamage * (targetPlayer.MaiLieuChan_ChuongLucKichHoat * 2.0 * 0.01));
                    if (shield > targetPlayer.NhanVat_AP)
                    {
                        shield = targetPlayer.NhanVat_AP;
                    }
                    targetPlayer.NhanVat_AP -= shield;
                }
                var damageDeal = finalDamage - shield;
                if (targetPlayer.TrungCapPhuHon_DiTinh != 0 && targetPlayer.AppendStatusList.ContainsKey(1000000954))
                {
                    damageDeal = 0;
                    targetPlayer.ShowBigPrint(targetPlayer.SessionID, 405);
                }
                GuiDi_CongKichSoLieu(damageDeal, attackType, targetPlayer.SessionID, shield);
                HandlePlayerAfterDamage(targetPlayer, targetDef, finalDamage, damageDeal);
            }
            else if (FindPlayers(100, targetPlayer))
			{
				GuiDi_DiDongSoLieu(targetPlayer.NhanVatToaDo_X, targetPlayer.NhanVatToaDo_Y, 10, 2);
			}
			else
			{
				PlayCw = null;
				var array6 = Array.FindAll(World.CauHinhBossTheoDame, s => s.Equals(FLD_PID.ToString()));
				if (!World.CauHinhBossTheoDame.Contains(FLD_PID.ToString()))
				{
					Play_null();
				}
				AutomaticAttack.Enabled = false;
				AutomaticMove.Enabled = true;
				Rxjh_X = Rxjh_cs_X;
				Rxjh_Y = Rxjh_cs_Y;
				Rxjh_Z = Rxjh_cs_Z;
				GuiDi_DiDongSoLieu(Rxjh_cs_X, Rxjh_cs_Y, 50, 1);
			}
		}
		else
		{
			if (targetPlayer.AutomaticAttack != null)
			{
				targetPlayer.AutomaticAttack.Enabled = false;
				targetPlayer.AutomaticAttack.Close();
				targetPlayer.AutomaticAttack.Dispose();
				targetPlayer.AutomaticAttack = null;
			}
			if (targetPlayer.AutomaticRecovery != null)
			{
				targetPlayer.AutomaticRecovery.Enabled = false;
				targetPlayer.AutomaticRecovery.Close();
				targetPlayer.AutomaticRecovery.Dispose();
				targetPlayer.AutomaticRecovery = null;
			}
			if (targetPlayer.InvincibleTimeCounter != null)
			{
				targetPlayer.InvincibleTimeCounter.Enabled = false;
				targetPlayer.InvincibleTimeCounter.Close();
				targetPlayer.InvincibleTimeCounter.Dispose();
			}
			PlayCw = null;
			var array7 = Array.FindAll(World.CauHinhBossTheoDame, s => s.Equals(FLD_PID.ToString()));
			if (!World.CauHinhBossTheoDame.Contains(FLD_PID.ToString()))
			{
				Play_null();
			}
			AutomaticAttack.Enabled = false;
			AutomaticMove.Enabled = true;
			Rxjh_X = Rxjh_cs_X;
			Rxjh_Y = Rxjh_cs_Y;
			Rxjh_Z = Rxjh_cs_Z;
			GuiDi_DiDongSoLieu(Rxjh_cs_X, Rxjh_cs_Y, 50, 1);
		}
	}

    private int HandlePlayerAfterDamage(Players targetPlayer,  double targetDef, int finalDamage, int damageDeal)
    {
        //targetPlayer.NhanVat_HP -= damageDeal;
		PlayerEvents.OnPlayerHpChanged(targetPlayer, damageDeal,null,PlayerEvents.HpChangeType.Damage,this);
        if (targetPlayer.Player_Job != 1 && targetPlayer.Player_Job != 7)
        {
            if (targetPlayer.Player_Job == 2)
            {
                if (RNG.Next(1, 100) <= targetPlayer.KIEM_DiHoa_TiepMoc)
                {
                    targetPlayer.ShowBigPrint(targetPlayer.SessionID, 26);
                    targetPlayer.AddBlood((int)(finalDamage * 0.5));
                    targetPlayer.CapNhat_HP_MP_SP();
                }
                if (RNG.Next(1, 100) <= targetPlayer.KIEM_ThangThien_3_KhiCong_HoaPhuongLamTrieu && targetPlayer.NhanVat_HP <= 0)
                {
                    targetPlayer.NhanVat_HP = 10;
                    targetPlayer.ShowBigPrint(targetPlayer.SessionID, 322);
                }
                if (damageDeal <= targetDef)
                {
                    if (!targetPlayer.NoKhi)
                    {
                        targetPlayer.NhanVat_SP++;
                    }
                }
                else if (!targetPlayer.NoKhi)
                {
                    targetPlayer.NhanVat_SP += 2;
                }
            }
            else if (targetPlayer.Player_Job == 3)
            {
                if (targetPlayer.THUONG_CuongThanHangThe != 0.0)
                {
                    if (!targetPlayer.NoKhi)
                    {
                        var num32 = (int)(targetPlayer.THUONG_CuongThanHangThe * 100.0) * 20;
                        targetPlayer.NhanVat_SP += num32 / 2;
                    }
                }
                else if (damageDeal <= targetDef)
                {
                    if (!targetPlayer.NoKhi)
                    {
                        targetPlayer.NhanVat_SP++;
                    }
                }
                else if (!targetPlayer.NoKhi)
                {
                    targetPlayer.NhanVat_SP += 2;
                }
            }
            else if (targetPlayer.Player_Job == 6)
            {
                var nINJA_KinhKhaChiNo = targetPlayer.NINJA_KinhKhaChiNo;
                if (nINJA_KinhKhaChiNo > 0.0)
                {
                    targetPlayer.NhanVat_SP += (int)(3.0 + targetPlayer.Player_Level * 0.5 * 0.01 * nINJA_KinhKhaChiNo);
                }
                else if (damageDeal <= targetDef)
                {
                    targetPlayer.NhanVat_SP++;
                }
                else
                {
                    targetPlayer.NhanVat_SP += 2;
                }
            }
            else if (targetPlayer.Player_Job == 8)
            {
                if (damageDeal <= targetDef)
                {
                    if (!targetPlayer.NoKhi)
                    {
                        targetPlayer.NhanVat_SP++;
                    }
                }
                else if (!targetPlayer.NoKhi)
                {
                    targetPlayer.NhanVat_SP += 2;
                }
                try
                {
                    if (RNG.Next(1, 100) <= targetPlayer.HanBaoQuan_TruyCotHapNguyen)
                    {
                        var num33 = damageDeal * (targetPlayer.HanBaoQuan_TruyCotHapNguyen * 0.01);
                        if (num33 <= 0.0)
                        {
                            num33 = 1.0;
                        }
                        targetPlayer.AddBlood((int)num33);
                        var num34 = 0;
                        if ((int)num33 > Max_Rxjh_HP)
                        {
                            num34 = Max_Rxjh_HP;
                        }
                        else
                        {
                            num34 = (int)num33;
                            if (Rxjh_HP > 0 && num34 > Rxjh_HP)
                            {
                                num34 = Rxjh_HP;
                            }
                        }
                        Play_Add(targetPlayer, num34);
                        Rxjh_HP -= num34;
                        if (Rxjh_HP <= 0)
                        {
                            double tienBac = (uint)ThuDuocTien(targetPlayer);
                            double num35 = ThuDuocKinhNghiem();
                            double lichLuyen = ThuDuocLichLuyen(targetPlayer);
                            if (targetPlayer.TrungCapPhuHon_KyDuyen != 0 && RNG.Next(1, 100) <= targetPlayer.TrungCapPhuHon_KyDuyen)
                            {
                                num35 *= 2.0;
                                targetPlayer.ShowBigPrint(targetPlayer.SessionID, 403);
                            }
                            targetPlayer.PhanPhoiKinhNghiemLichLuyenTienTai(targetPlayer, this, num35, lichLuyen, tienBac, 0.0);
                            GuiDiTuVongSoLieuWrapper(NPC_SessionID);
                        }
                    }
                }
                catch (Exception ex)
                {
                    LogHelper.WriteLine(LogLevel.Error, "Event tấn công tự động HanBaoQuan [TruyCotHapNguyen] Chống thương tích error：" + ex);
                }
            }
            else if (targetPlayer.Player_Job == 9)
            {
                if (RNG.Next(1, 100) <= targetPlayer.DamHoaLien_DiHoa_TiepMoc)
                {
                    targetPlayer.ShowBigPrint(targetPlayer.SessionID, 26);
                    targetPlayer.AddBlood(finalDamage * 2);
                    targetPlayer.CapNhat_HP_MP_SP();
                }
                if (RNG.Next(1, 100) <= targetPlayer.DamHoaLien_ThangThien_3_KhiCong_HoaPhuongLamTrieu && targetPlayer.NhanVat_HP <= 0)
                {
                    targetPlayer.NhanVat_HP = 10;
                    targetPlayer.ShowBigPrint(targetPlayer.SessionID, 322);
                }
                if (damageDeal <= targetDef)
                {
                    if (!targetPlayer.NoKhi)
                    {
                        targetPlayer.NhanVat_SP++;
                    }
                }
                else if (!targetPlayer.NoKhi)
                {
                    targetPlayer.NhanVat_SP += 2;
                }
            }
            else if (targetPlayer.Player_Job == 10)
            {
                if (targetPlayer.QuyenSu_CuongThanHangThe != 0.0)
                {
                    if (!targetPlayer.NoKhi)
                    {
                        var num36 = (int)(targetPlayer.QuyenSu_CuongThanHangThe * 100.0) * 10;
                        targetPlayer.NhanVat_SP += num36 / 2;
                    }
                }
                else if (damageDeal <= targetDef)
                {
                    if (!targetPlayer.NoKhi)
                    {
                        targetPlayer.NhanVat_SP++;
                    }
                }
                else if (!targetPlayer.NoKhi)
                {
                    targetPlayer.NhanVat_SP += 2;
                }
            }
            else if (targetPlayer.Player_Job == 11)
            {
                if (damageDeal <= targetDef)
                {
                    if (!targetPlayer.NoKhi)
                    {
                        targetPlayer.NhanVat_SP++;
                    }
                }
                else if (!targetPlayer.NoKhi)
                {
                    targetPlayer.NhanVat_SP += 2;
                }
                var num37 = targetPlayer.CharacterMax_AP / 2;
                if (targetPlayer.MaiLieuChan_ChuongLucKhoiPhuc > 0.0 && targetPlayer.NhanVat_AP < num37 && RNG.Next(1, 120) <= targetPlayer.MaiLieuChan_ChuongLucKhoiPhuc)
                {
                    targetPlayer.NhanVat_AP = num37;
                    targetPlayer.ShowBigPrint(targetPlayer.SessionID, 801);
                }
                if (targetPlayer.MaiLieuChan_PhanNoBaoPhat > 0.0 && RNG.Next(1, 100) <= 40 && targetPlayer.NoKhi_Point < 3 && targetPlayer.NhanVat_BatTu == 0)
                {
                    targetPlayer.NoKhi_Point++;
                }
            }
            else if (damageDeal <= targetDef)
            {
                if (!targetPlayer.NoKhi)
                {
                    targetPlayer.NhanVat_SP++;
                }
            }
            else if (!targetPlayer.NoKhi)
            {
                targetPlayer.NhanVat_SP += 2;
            }
        }
        else
        {
            if (damageDeal <= targetDef)
            {
                if (!targetPlayer.NoKhi)
                {
                    targetPlayer.NhanVat_SP++;
                }
            }
            else if (!targetPlayer.NoKhi)
            {
                targetPlayer.NhanVat_SP += 2;
            }
            try
            {
                if (damageDeal <= 0)
                {
                    damageDeal = 1;
                }
                if (RNG.Next(1, 120) <= ((targetPlayer.Player_Job != 1) ? (targetPlayer.CAMSU_ThangThien_2_KhiCong_TamDamAnhNguyet + 10.0) : (targetPlayer.QuaiVat_PhanSatThuong_TiLe + 10.0)) && damageDeal > 0)
                {
                    var num38 = 0;
                    if (targetPlayer.Player_Job == 7)
                    {
                        targetPlayer.ShowBigPrint(targetPlayer.SessionID, 391);
                        GuiDi_PhanSatThuong_CongKichSoLieu(damageDeal, targetPlayer.SessionID);
                        num38 = damageDeal;
                    }
                    if ((targetPlayer.Player_Job == 1 || targetPlayer.Player_Job == 8) && targetPlayer.QuaiVat_PhanSatThuong_TiLe != 0.0 && RNG.Next(1, 100) <= targetPlayer.QuaiVat_PhanSatThuong_TiLe + targetPlayer.DAO_ThangThien_2_KhiCong_CungDoMatLo)
                    {
                        if ((targetPlayer.Player_Job == 1 || targetPlayer.Player_Job == 8) && targetPlayer.DAO_ThangThien_2_KhiCong_CungDoMatLo != 0.0 && RNG.Next(1, 100) <= targetPlayer.DAO_ThangThien_2_KhiCong_CungDoMatLo)
                        {
                            targetPlayer.ShowBigPrint(targetPlayer.SessionID, 19);
                            GuiDi_PhanSatThuong_CongKichSoLieu(damageDeal * 2, targetPlayer.SessionID);
                            num38 = damageDeal * 2;
                        }
                        else
                        {
                            targetPlayer.ShowBigPrint(targetPlayer.SessionID, 15);
                            GuiDi_PhanSatThuong_CongKichSoLieu(damageDeal, targetPlayer.SessionID);
                            num38 = damageDeal;
                        }
                    }
                    var num39 = 0;
                    if (num38 > Max_Rxjh_HP)
                    {
                        num39 = Max_Rxjh_HP;
                    }
                    else
                    {
                        num39 = num38;
                        if (Rxjh_HP > 0 && num39 > Rxjh_HP)
                        {
                            num39 = Rxjh_HP;
                        }
                    }
                    Play_Add(targetPlayer, num39);
                    Rxjh_HP -= num39;
                    if ((!NPCDeath && Rxjh_HP <= 0) || (FLD_BOSS == 1 && (Rxjh_HP <= 0 || NPCDeath)))
                    {
                        double tienBac2 = (uint)ThuDuocTien(targetPlayer);
                        double num40 = ThuDuocKinhNghiem();
                        double lichLuyen2 = ThuDuocLichLuyen(targetPlayer);
                        if (targetPlayer.TrungCapPhuHon_KyDuyen != 0 && RNG.Next(1, 100) <= targetPlayer.TrungCapPhuHon_KyDuyen)
                        {
                            num40 *= 2.0;
                            targetPlayer.ShowBigPrint(targetPlayer.SessionID, 403);
                        }
                        var array4 = Array.FindAll(World.CauHinhBossTheoDame, s => s.Equals(FLD_PID.ToString()));
                        if (World.CauHinhBossTheoDame.Contains(FLD_PID.ToString()))
                        {
							// Bỏ tính toán boss theo team dmg bình thường
                            // var playGjClass = FindMaxDame(PlayerTargetList);
                            // var check_team = FindMaxDameTeam(PlayerTargetList);
                            // if (playGjClass.Gjxl > check_team.Gjxl_team && Rxjh_Map == 40101)
                            // {
                            //     var dropRate = Math.Round((double)(playGjClass.Gjxl * 100 / Max_Rxjh_HP), 2) + "%";
                            //     var attacker = targetPlayer.GetCharacterData(playGjClass.PlayID);
                            //     targetPlayer.PhanPhoiKinhNghiemLichLuyenTienTai(attacker, this, num40, lichLuyen2, tienBac2, 0.0);

                            // }
                            // else
                            // {
                            //     var captainName = "";
                            //     if (World.WToDoi.TryGetValue(check_team.TeamID, out var value3))
                            //     {
                            //         captainName = value3.DoiTruongTen;
                            //     }
                            //     var text4 = Math.Round(check_team.Gjxl_team * 100.0 / Max_Rxjh_HP, 2) + "%";
                            //     var source = PlayerTargetList.Where(k => k.TeamID == check_team.TeamID);
                            //     var attacker = targetPlayer.GetCharacterData(source.FirstOrDefault().PlayID);
                            //     targetPlayer.PhanPhoiKinhNghiemLichLuyenTienTai(attacker, this, num40, lichLuyen2, tienBac2, 0.0);
                            // }
                        }
                        else
                        {
                            targetPlayer.PhanPhoiKinhNghiemLichLuyenTienTai(targetPlayer, this, num40, lichLuyen2, tienBac2, 0.0);
                        }
                        GuiDiTuVongSoLieuWrapper(NPC_SessionID);
                    }
                }
            }
            catch (Exception ex2)
            {
                LogHelper.WriteLine(LogLevel.Error, "Automatic Attack Event Nhạc công /Dao Khách phản tổn thương error：" + ex2);
            }
        }
        if (targetPlayer.FLD_TrangBi_ThemVao_Phan_NoKhi > 0 && !targetPlayer.NoKhi)
        {
            targetPlayer.NhanVat_SP += targetPlayer.FLD_TrangBi_ThemVao_Phan_NoKhi;
        }
        if (targetPlayer.FLD_TrangBi_ThemVao_TrungDocXacSuatTiLePhanTram > 0.0 && RNG.Next(1, 100) <= targetPlayer.FLD_TrangBi_ThemVao_TrungDocXacSuatTiLePhanTram && !ContainsKeyInAbnormalState(3))
        {
            TrangThai_BatThuong.Add(3, new(this, PlayerWid, 60000, 3, 0.0));
        }
        if (targetPlayer.NhanVat_HP <= 0)
        {
            targetPlayer.HeThongNhacNho("Đại hiệp bị yêu quái cấp [" + Level + "] hạ sát, thật đáng tiếc!", 10, "Thiên cơ các");
            if (targetPlayer.TeamID != 0 && World.WToDoi.TryGetValue(targetPlayer.TeamID, out var value4))
            {
                foreach (var value7 in value4.ToDoi_NguoiChoi.Values)
                {
                    if (value7.SessionID != targetPlayer.SessionID)
                    {
                        value7.HeThongNhacNho("Bị yêu quái đánh trọng thương, phải nhập y quán dưỡng thương!!", 2, targetPlayer.CharacterName ?? "");
                    }
                }
            }
            if (World.ChetCoMatKinhNghiemKhong == 1 && targetPlayer.Player_Level > 10)
            {
                var num41 = ((long)World.lever[targetPlayer.Player_Level + 1] - (long)World.lever[targetPlayer.Player_Level]) / 1000;
                if (targetPlayer.PublicDrugs != null)
                {
                    if (targetPlayer.KiemTra_Phu() && targetPlayer.KiemTra_Phu2())
                    {
                    }
                }
                else
                {
                    var num42 = RNG.Next(1, 100);
                    num41 = ((num42 >= 1 && num42 <= 10) ? num41 : ((num42 >= 11 && num42 <= 20) ? (num41 * 2) : ((num42 >= 21 && num42 <= 30) ? (num41 * 3) : ((num42 >= 31 && num42 <= 40) ? (num41 * 4) : ((num42 >= 41 && num42 <= 50) ? (num41 * 5) : ((num42 >= 51 && num42 <= 60) ? (num41 * 6) : ((num42 >= 61 && num42 <= 70) ? (num41 * 7) : ((num42 >= 71 && num42 <= 80) ? (num41 * 8) : ((num42 < 81 || num42 > 90) ? (num41 * 10) : (num41 * 9))))))))));
                }
                if (targetPlayer.FLD_TrangBi_ThemVao_TuVong_TonThat_KinhNghiem_GiamBot > 0.0)
                {
                    num41 = (long)(num41 * (1.0 - targetPlayer.FLD_TrangBi_ThemVao_TuVong_TonThat_KinhNghiem_GiamBot));
                    if (num41 < 0)
                    {
                        num41 = 0L;
                    }
                }
                for (var l = 0; l < 15; l++)
                {
                    if (BitConverter.ToInt32(targetPlayer.EquippedInTheEquipmentSlot[l].VatPham_ID, 0) == 700004)
                    {
                        num41 = 0L;
                        targetPlayer.EquippedInTheEquipmentSlot[l].VatPham_byte = new byte[World.Item_Db_Byte_Length];
                        targetPlayer.KhoiTaoChungToiDa_TrangBiVatPham();
                        break;
                    }
                }
                for (var m = 0; m < targetPlayer.Item_In_Bag.Length; m++)
                {
                    var num43 = 0;
                    if (BitConverter.ToInt32(targetPlayer.Item_In_Bag[m].VatPham_ID, 0) == 1008000142)
                    {
                        targetPlayer.VatPham_GiamDi_SoLuong_DoBen(m, num43);
                        if (num43 > 0)
                        {
                            num43 = 0;
                        }
                        num41 = 0L;
                        targetPlayer.HeThongNhacNho("Đại hiệp vừa thi triển Sinh Tử Phù, thoát khỏi lằn ranh sinh tử!");
                        targetPlayer.Item_In_Bag[m].VatPham_byte = new byte[World.Item_Db_Byte_Length];
                        targetPlayer.Init_Item_In_Bag();
                        break;
                    }
                }
                if (targetPlayer.GetAddState(1008000160) || targetPlayer.GetAddState(1008000159))
                {
                    num41 = 0L;
                }
                targetPlayer.CharacterExperience -= num41;
                targetPlayer.TinhToan_NhanVatCoBan_DuLieu3();
                targetPlayer.UpdateKinhNghiemVaTraiNghiem();
            }
            AutomaticAttack.Enabled = false;
            AutomaticMove.Enabled = true;
            Rxjh_X = Rxjh_cs_X;
            Rxjh_Y = Rxjh_cs_Y;
            Rxjh_Z = Rxjh_cs_Z;
            GuiDi_DiDongSoLieu(Rxjh_cs_X, Rxjh_cs_Y, 50, 1);
            targetPlayer.NhanVat_HP = 0;
            targetPlayer.Death();
            PlayCw = null;
            var array5 = Array.FindAll(World.CauHinhBossTheoDame, s => s.Equals(FLD_PID.ToString()));
            if (!World.CauHinhBossTheoDame.Contains(FLD_PID.ToString()))
            {
                Play_null();
            }
        }
        targetPlayer.CapNhat_HP_MP_SP();
        return damageDeal;
    }

    private void HandleBossMagicAttack(Players targetPlayer, int countToSkill)
    {
        if(isDoingMech)
            return;

        int aoe = 100;
        int base_attack = (int)(FLD_AT * 1);
        int monsterSkillAnimation = 1;
        double currentHPPercent = (double)Rxjh_HP * 100 / Max_Rxjh_HP;
        int timeAnimation = 2; // process bar time animation


        switch(FLD_PID)
        {
            case 15423:
                if (IsWorldBoss)
                {

                    HandleBossStages15423(targetPlayer, currentHPPercent, countToSkill);
                }
                else
                {
                    NormalBossBehavior(targetPlayer, countToSkill, base_attack, monsterSkillAnimation, timeAnimation, aoe);
                }
                break;
            case 15419:
            case 15420:
            case 15421:
            case 15422:
            case 15424:
            case 16556:
            case 16555:
            case 16786:
            case 16787:
            case 16788:
                BossMagicAttack(targetPlayer, base_attack, monsterSkillAnimation, timeAnimation, countToSkill, aoe);
                break;
         default:
                BossMagicAttack(targetPlayer, base_attack, monsterSkillAnimation, timeAnimation, countToSkill, aoe);
                isCastingMagic = false;
                attackCount = 0;
                break;
        }
        return;
    }

    private void HandleBossStages15423(Players targetPlayer, double currentHPPercent, int countToSkill)
    {
        int base_attack = (int)(FLD_AT * 1.5);
        int monsterSkillAnimation = 1;
        int timeAnimation = 2; //
        int aoe = 100;
        int[] summonId = { 15231, 15232, 15233, 15234, 15235, 15237, 15238, 15239 };

        if(FLD_PID == 15423)
        {
            LogHelper.WriteLine(LogLevel.Error, "Stage " + BossStage + " Mech " + isDoingMech + " HP " + currentHPPercent + " PID " + FLD_PID + " Casting "+isCastingMagic);
        }
		//NpcSpeak("Chuẩn bị chết đi!!!");
        if(BossStage == 0 && currentHPPercent <= 75  && !isDoingMech && !isCastingMagic)
        {
            TransitionToNextStage(targetPlayer, 3, 1, summonId, 75, 50);
            BossStage++;
        }
        else if((BossStage == 1 && !isDoingMech) || (BossStage == 3 && !isDoingMech))
        {
            ApplyStageEffects(targetPlayer, 150);
            BossStage++;
        }
        else if (BossStage == 2 && currentHPPercent <= 25 && !isDoingMech && !isCastingMagic)
        {
            TransitionToNextStage(targetPlayer, 3, 1, summonId, 25, 75);
            BossStage++;
        }
        else if (BossStage == 3 && !isDoingMech)
        {
            ApplyStageEffects(targetPlayer, 150);
            BossStage++;
        }
        else
        {
            NormalBossBehavior(targetPlayer, countToSkill, base_attack, monsterSkillAnimation, timeAnimation, aoe);
        }
    }

    public int BossStage { get; set; }
    public int Rxjh_Gold { get; internal set; }
    public int FLD_QItemDrop { get; internal set; }
    public int FLD_QDropPP { get; internal set; }

    private void NormalBossBehavior(Players targetPlayer, int countToSkill, int base_attack, int monsterSkillAnimation, int timeAnimation, int aoe)
    {
        if (attackCount >= countToSkill)
        {
            BossMagicAttack(targetPlayer, base_attack, monsterSkillAnimation, timeAnimation, countToSkill, aoe);
        }
        else
        {
             HandleNpcAttackPlayer(targetPlayer);
        }
    }

    private void TransitionToNextStage(Players targetPlayer, int animation, int stage, int[] summonId, int hpFactor, int positionFactor)
    {
        try
        {
            LogHelper.WriteLine(LogLevel.Info, "Stage " + stage);
            var monsterSkillAnimation = animation;
            Send8705(targetPlayer, monsterSkillAnimation, 10, 1);
            //SummonNpcs(summonId, positionFactor, 10000);
            isDoingMech = true;

            // Setup time for the next action
            Task.Delay(10000).ContinueWith(_ =>
            {
                LogHelper.WriteLine(LogLevel.Error, "Delay mech");
                monsterSkillAnimation = 4;
                Send8705(targetPlayer, monsterSkillAnimation, 2, 1);
                isDoingMech = false;
            });
            }
        catch (Exception Ex)
        {
            LogHelper.WriteLine(LogLevel.Error, "TransitionToNextStage Error " + Ex.Message);
        }

    }

    private void SummonNpcs(int[] summonId, int positionFactor, int timetoDie)
    {
        try
        {
            int normalSum = RNG.Next(0, summonId.Length - 1);
            int specialSum;
            do
            {
                specialSum = RNG.Next(0, summonId.Length-1);
            }
            while (specialSum == normalSum);

            for(int i = 0; i < 10; i++)
            {
                var npc = World.AddNpcNeo(summonId[normalSum], Rxjh_cs_X + RNG.Next(-positionFactor, positionFactor), Rxjh_cs_Y + RNG.Next(-positionFactor, positionFactor), Rxjh_Map, string.Empty, 0, 0, 0, false, 0, timetoDie);
                //summonedList.Add(npc.FLD_INDEX, npc);AddN
            }
            var npcx = World.AddNpcNeo(summonId[specialSum], Rxjh_cs_X + RNG.Next(-positionFactor, positionFactor), Rxjh_cs_Y + RNG.Next(-positionFactor, positionFactor), Rxjh_Map, string.Empty, 0, 0, 0, false, 1, timetoDie);
            LogHelper.WriteLine(LogLevel.Error, "marked Type " + npcx.NPC_SessionID);
            //summonedList.Add(npcx.FLD_INDEX, npcx);
        }
        catch (Exception ex)
        {
            LogHelper.WriteLine(LogLevel.Info, "SummonNpcs Error " + ex.Message);
        }

    }
	private void TaoCo()
    {
        if(MarkedType == 1)
        {
            if(Rxjh_HP <= 0)
            {
                LogHelper.WriteLine(LogLevel.Error, "--Quai bi giet");
            }
            LogHelper.WriteLine(LogLevel.Error, "--MarkType = 1");
            World.AddNpcNeo(15732, Rxjh_cs_X, Rxjh_cs_Y, Rxjh_Map, string.Empty, 0, 0, 0, false, 0, 10000);
        }
    }

    private void ApplyStageEffects(Players targetPlayer, int range)
    {
        LogHelper.WriteLine(LogLevel.Error, "Stage Effects");
        foreach(var item in PlayList.Values)
        {
            if(FindPlayers(range, item))
            {
                if(!item.LookInNpc(15732, 65))
                {
                    //item.HeThongNhacNho("Khong dung gan 15732");
                    int rate = 10;
                    if(BossStage == 1)
                    {
                        rate = 20;
                    }
                    ApplyStatusEffects(item, rate);
                }
            }
        }
        Send8710(targetPlayer, 100, 0);
    }

    private void ApplyStatusEffects(Players player, int rate = 20)
    {
        X_Di_Thuong_Trang_Thai_Loai rootTarget = new(player, 10000, 4, 0.0);
        player.TrangThai_BatThuong.Add(4, rootTarget);

        X_Di_Thuong_Trang_Thai_Loai bleed = new(player, 10000, 10, player.CharacterMax_HP / rate);
        player.TrangThai_BatThuong.Add(10, bleed);
        bleed.TrangThai_BatThuong_LoaiChayMau(player.CharacterMax_HP / rate);

        X_Them_Vao_Trang_Thai_Loai value14 = new(player, 10000, 700667, 0);
        player.AppendStatusList.Add(700667, value14);
        player.StatusEffect(BitConverter.GetBytes(700667), 1, 10000);
        player.ShowBigPrint(player.SessionID, 1014);
    }

    private void BossMagicHeal(Players targetPlayer, int healingValue, int countToSkill, int aoe, int processBar = 1)
    {
        if(attackCount >= countToSkill && isCastingMagic)
        {
            AutomaticAttack.Interval = 2500.0;
            //Rxjh_HP += healingValue;
            UpdateHP('+', healingValue);
            Send4238(targetPlayer, healingValue);
            isCastingMagic = false;
            attackCount = 0;
        }
    }
	public void UpdateHP(char operation, int value)
        {
            switch(operation)
            {
                case '+':
                    Rxjh_HP += value;
                    break;
                case '-':
                    Rxjh_HP -= value;
                    if(Rxjh_HP < 0)
                        Rxjh_HP = 0; // Giả sử HP không thể âm
                    break;
                case '*':
                    Rxjh_HP *= value;
                    break;
                case '/':
                    if(value != 0)
                        Rxjh_HP /= value;
                    else
                        throw new ArgumentException("Không thể chia cho 0.");
                    break;
                default:
                    throw new ArgumentException("Phép toán không hợp lệ.");
            }

            if(Rxjh_HP > Max_Rxjh_HP)
                Rxjh_HP = Max_Rxjh_HP;
        }

    // Đối tượng khóa để đồng bộ hóa việc cập nhật HP
    private readonly object _hpLock = new ();

    public void UpdateHP(char operation, int value, int playerSession, string playerName,int playerLevel, string guildName, int guildId, bool skipbs = false)
    {
        if (CurrentZone != null && WorldId != World.ServerID.ToString() && !skipbs)
        {
            LogHelper.WriteLine(LogLevel.Info, "Gửi Update HP qua cross-server");

            World.conn.SendCrossServerAction(
                CurrentZone.ID,
                "ATTACK_NPC",
                playerSession,
                NPC_SessionID,
                WorldId,
                operation,
                value,
				playerSession,
				playerName,
				playerLevel,
				guildName,
				guildId,
				Level
            );
            return;
        }

        LogHelper.WriteLine(LogLevel.Info, "Update HP bình thường");

        // Sử dụng khóa để tránh race condition
        lock (_hpLock)
        {
            // Xử lý UpdateHP bình thường nếu không phải cross-server
            switch (operation)
            {
                case '+':
                    _Rxjh_HP += value;
                    break;
                case '-':
                    var damage = value;
                    _Rxjh_HP -= value;

                    if (_Rxjh_HP < 0)
                    {
                        damage += _Rxjh_HP;
                        _Rxjh_HP = 0;
                    }
                    if (IsWorldBoss && World.List_WorldBossContribute.TryGetValue(ID, out var contributes))
                    {
                        // Xử lý contribute liên server
                        contributes.UpdateContribute(World.ServerID,playerSession,playerName, damage, 1);
                    }
                    break;
                case '*':
                    _Rxjh_HP *= value;
                    break;
                case '/':
                    if (value != 0)
                        _Rxjh_HP /= value;
                    else
                        throw new ArgumentException("Không thể chia cho 0.");
                    break;
                default:
                    throw new ArgumentException("Phép toán không hợp lệ.");
            }

            if (_Rxjh_HP > _Max_Rxjh_HP)
                _Rxjh_HP = _Max_Rxjh_HP;

           // Kiểm tra nếu HP = 0 thì xử lý chết
            if (_Rxjh_HP <= 0 && !NPCDeath)
            {
                // Gọi phương thức xử lý khi NPC chết
                GuiDiTuVongSoLieuWrapper(playerSession);
            }
        }
    }

    private void BossMagicAttack(Players targetPlayer, int atkDmgBoss, int monsterSkillAnimation, int timeAnimation, int countToSkill, int aoe, int processBar = 1)
    {
        if(attackCount >= countToSkill && !isCastingMagic)
        {
            AutomaticAttack.Interval = 2000.0;
            Send8705(targetPlayer, monsterSkillAnimation, timeAnimation, processBar);
            isCastingMagic = true;
            return;
        }
        if(attackCount >= countToSkill && isCastingMagic)
        {
            AutomaticAttack.Interval = 2500.0;
            Send8710(targetPlayer, aoe, atkDmgBoss);
            isCastingMagic = false;
            attackCount = 0;
        }
    }

    private void Send4238(Players targetPlayer, int attackDmg)
    {
        var res = Converter.HexStringToByte("AA5514004C7B8E100C004C7B0000010100003075000055AA");
        System.Buffer.BlockCopy(BitConverter.GetBytes(targetPlayer.SessionID), 0, res, 4, 2);
        System.Buffer.BlockCopy(BitConverter.GetBytes(NPC_SessionID), 0, res, 10, 2);
        System.Buffer.BlockCopy(BitConverter.GetBytes(1), 0, res, 14, 1);
        System.Buffer.BlockCopy(BitConverter.GetBytes(1), 0, res, 15, 1);
        System.Buffer.BlockCopy(BitConverter.GetBytes(attackDmg), 0, res, 0x12, 4);
        GuiDiTruocMatPhamViQuangBaSoLieu(res);
    }

    private void Send8710(Players targetPlayer, int aoe, int attackDmg)
    {
        var type = 1;
        var type2 = 1;

        byte[] res = Converter.HexStringToByte("AA556401855206225C010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000400D030000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000055AA");
        System.Buffer.BlockCopy(BitConverter.GetBytes(NPC_SessionID), 0, res, 4, 2);
        System.Buffer.BlockCopy(BitConverter.GetBytes(type), 0, res, 14, 2);
        System.Buffer.BlockCopy(BitConverter.GetBytes(type2), 0, res, 16, 2);

        System.Buffer.BlockCopy(BitConverter.GetBytes(targetPlayer.NhanVatToaDo_X), 0, res, 22, 4);
        System.Buffer.BlockCopy(BitConverter.GetBytes(targetPlayer.NhanVatToaDo_Y), 0, res, 30, 4);
        System.Buffer.BlockCopy(BitConverter.GetBytes(targetPlayer.SessionID), 0, res, 10, 4);
        System.Buffer.BlockCopy(BitConverter.GetBytes(PlayList.Values.Count), 0, res, 18, 4);
        int i = 0;
        foreach(Players player in PlayList.Values)
        {
            if(FindPlayers(aoe, player))
            {
                System.Buffer.BlockCopy(BitConverter.GetBytes(player.SessionID), 0, res, 34 + i * 4, 4);
                System.Buffer.BlockCopy(BitConverter.GetBytes(attackDmg), 0, res, 114 + i * 4, 4);
                i++;
                //player.NhanVat_HP -= attackDmg;
				//PlayerEvents.OnPlayerHpChanged(player, attackDmg,null,PlayerEvents.HpChangeType.Damage,this);
                HandlePlayerAfterDamage(player, 0,attackDmg,attackDmg);
            }
        }
        GuiDiTruocMatPhamViQuangBaSoLieu(res);
    }

    private void SendMagicAnimation()
    {
        var array = Converter.HexStringToByte("aa551000c8781022080001000000c878000055aa");
        System.Buffer.BlockCopy(BitConverter.GetBytes(1), 0, array, 10, 4);
        System.Buffer.BlockCopy(BitConverter.GetBytes(NPC_SessionID), 0, array, 14, 4);
        GuiDiTruocMatPhamViQuangBaSoLieu(array);
    }

    private void Send8705(Players targetPlayer, int ani, int time, int processbar)
    {
        int unknow = 11; // 10 11 66
        int monsterSkillAnimation = ani; // 1 2
        int timeAnimation = time; //  process bar time animation
        int headIcon = 0; // 0 1
        int processBar = processbar; // process bar on off
        int unknow6 = 2;
        //targetPlayer.HeThongLogMessage($"{FLD_PID} Send 8705 u1 {unknow} ani {monsterSkillAnimation} head {headIcon} time {timeAnimation}",7,"");
        byte[] array3 = Converter.HexStringToByte("aa553c004e7b01223400da01000066000200020000000000000000000000000000000000000000000000000000000000000000000000000000000000000055aa");
        System.Buffer.BlockCopy(BitConverter.GetBytes(NPC_SessionID), 0, array3, 4, 2);
        System.Buffer.BlockCopy(BitConverter.GetBytes(targetPlayer.SessionID), 0, array3, 10, 4);
        System.Buffer.BlockCopy(BitConverter.GetBytes(unknow), 0, array3, 14, 2);
        System.Buffer.BlockCopy(BitConverter.GetBytes(monsterSkillAnimation), 0, array3, 16, 2);
        System.Buffer.BlockCopy(BitConverter.GetBytes(timeAnimation), 0, array3, 18, 2);
        System.Buffer.BlockCopy(BitConverter.GetBytes(headIcon), 0, array3, 20, 2);
        System.Buffer.BlockCopy(BitConverter.GetBytes(processBar), 0, array3, 22, 2);
        System.Buffer.BlockCopy(BitConverter.GetBytes(unknow6), 0, array3, 24, 2);
        GuiDiTruocMatPhamViQuangBaSoLieu(array3);
    }
    public List<NpcClass> DanhNhieuMucTieu_TraTimPhamVi_Npc2(Players player, int SoLuong)
	{
		try
		{
			List<NpcClass> list = new();
			var num = 0;
			if (player.NpcList != null)
			{
				foreach (var value in player.NpcList.Values)
				{
					if (!value.NPCDeath && value.IsNpc == 0 && LookInNpc(25, value) && value._FLD_INDEX != _FLD_INDEX)
					{
						list.Add(value);
						if (num >= SoLuong)
						{
							break;
						}
						num++;
					}
				}
			}
			return list;
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Đánh nhiều mục tiêu Nhìn vào Npc 11 error：" + ex);
			return null;
		}
	}

	public List<NpcClass> DanhNhieuMucTieu_TraTimPhamVi_Npc2_800MET(Players player, int SoLuong)
	{
		try
		{
			List<NpcClass> list = new();
			var num = 0;
			if (player.NpcList != null)
			{
				foreach (var value in player.NpcList.Values)
				{
					if (!value.NPCDeath && value.IsNpc == 0 && LookInNpc(85, value) && value._FLD_INDEX != _FLD_INDEX)
					{
						list.Add(value);
						if (num >= SoLuong)
						{
							break;
						}
						num++;
					}
				}
			}
			return list;
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Đánh nhiều mục tiêu Nhìn vào Npc 22 error：" + ex);
			return null;
		}
	}

	public List<NpcClass> DanhNhieuMucTieu_TraTimPhamVi_Npc2_200MET_44(Players player, int SoLuong)
	{
		try
		{
			List<NpcClass> list = new();
			var num = 0;
			if (player.NpcList != null)
			{
				foreach (var value in player.NpcList.Values)
				{
					if (!value.NPCDeath && value.IsNpc == 0 && LookInNpc(85, value) && value._FLD_INDEX != _FLD_INDEX && !value.ContainsKeyInAbnormalState(44))
					{
						list.Add(value);
						if (num >= SoLuong)
						{
							break;
						}
						num++;
					}
				}
			}
			return list;
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Đánh nhiều mục tiêu Nhìn vào Npc 33 error：" + ex);
			return null;
		}
	}

	public List<NpcClass> DanhNhieuMucTieu_TraTimPhamVi_Npc2_200MET_GAYDAME(Players player, int SoLuong)
	{
		try
		{
			List<NpcClass> list = new();
			var num = 0;
			if (player.NpcList != null)
			{
				foreach (var value in player.NpcList.Values)
				{
					if (!value.NPCDeath && value.IsNpc == 0 && LookInNpc(85, value) && value != this)
					{
						list.Add(value);
						if (num >= SoLuong)
						{
							break;
						}
						num++;
					}
				}
			}
			return list;
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Đánh nhiều mục tiêu Nhìn vào Npc 44 error：" + ex);
			return null;
		}
	}

	public static void UpdateNPCSoLieu(Dictionary<int, NpcClass> NpcList, Players Playe)
	{
		try
		{
			if (NpcList == null || NpcList.Count <= 0)
			{
				return;
			}
			using SendingClass sendingClass = new();
			sendingClass.Write4(NpcList.Count);
			foreach (var value in NpcList.Values)
			{
				sendingClass.Write4(value.NPC_SessionID);
				sendingClass.Write4(value.NPC_SessionID);
				sendingClass.Write2(value.FLD_PID);

				sendingClass.Write2(1);
				sendingClass.Write4(value.Rxjh_HP);
				sendingClass.Write4(value.Max_Rxjh_HP);
				//20
				sendingClass.Write(value.Rxjh_X);
				sendingClass.Write(value.Rxjh_Z);
				sendingClass.Write(value.Rxjh_Y);
				//+32
				sendingClass.Write4(1082130432);
				sendingClass.Write(value.FLD_FACE1);
				sendingClass.Write(value.FLD_FACE2);
				sendingClass.Write(value.Rxjh_X);
				sendingClass.Write(value.Rxjh_Z);
				sendingClass.Write(value.Rxjh_Y);
				sendingClass.Write4(0);
				// 32+28 = 60
				if (value.FLD_BOSS == 1)
				{
					sendingClass.Write4(1);
					sendingClass.Write4(10);
					sendingClass.Write2(0);
					sendingClass.Write2(0);
				}
				else
				{
					sendingClass.Write4(0);
					sendingClass.Write4(10);
					sendingClass.Write4(0);
				}
				sendingClass.Write4(2359296);
				sendingClass.Write4(uint.MaxValue);
				if (value.NPCDeath)
				{
					value.UpdateNPC_DeXoaSoLieu(Playe);
					value.LamMoi_NPCDeathSoLieu(Playe);
				}
			}
			Playe.Client?.SendPak(sendingClass, 26368, Playe.SessionID);
		}
		catch
		{
			LogHelper.WriteLine(LogLevel.Error, "Lỗi tại đây update NPC SoLieu 1212 !!");
		}
	}

	public static NpcClass GetNpcBySessionAndWorldId(int sessionId, string worldId)
	{
		// Nếu worldId trùng với server hiện tại, tìm NPC trong server hiện tại
		if (worldId == World.ServerID.ToString())
		{
			foreach (var map in World.MapList.Values)
			{
				if (map.npcTemplate.TryGetValue(sessionId, out var npc))
				{
					return npc;
				}
			}
		}
		else
		{
			// Nếu worldId khác với server hiện tại, tìm NPC trong danh sách NPC clone
			if (World.CrossServerNpcMapping.TryGetValue(worldId, out var mapping) &&
				mapping.TryGetValue(sessionId, out var localNpcSessionId))
			{
				foreach (var map in World.MapList.Values)
				{
					if (map.npcTemplate.TryGetValue(localNpcSessionId, out var npc))
					{
						return npc;
					}
				}
			}
		}

		return null;
	}

	public static void UpdateNPC_DeXoaSoLieu(Dictionary<int, NpcClass> NpcList, Players Playe)
	{
		try
		{
			if (NpcList == null || NpcList.Count <= 0)
			{
				return;
			}
			using SendingClass sendingClass = new();
			sendingClass.Write4(NpcList.Count);
			foreach (var value in NpcList.Values)
			{
				sendingClass.Write4(value.NPC_SessionID);
				sendingClass.Write4(value.NPC_SessionID);
				sendingClass.Write2(value.FLD_PID);
				sendingClass.Write2(1);
				sendingClass.Write4(value.Rxjh_HP);
				sendingClass.Write4(value.Max_Rxjh_HP);

				sendingClass.Write(value.Rxjh_X);
				sendingClass.Write(value.Rxjh_Z);
				sendingClass.Write(value.Rxjh_Y);

				sendingClass.Write4(1082130432);
				sendingClass.Write(value.FLD_FACE1);
				sendingClass.Write(value.FLD_FACE2);

				sendingClass.Write(value.Rxjh_X);
				sendingClass.Write(value.Rxjh_Z);
				sendingClass.Write(value.Rxjh_Y);

				if (value.FLD_BOSS == 1)
				{
					sendingClass.Write4(0);
					sendingClass.Write4(1);
					sendingClass.Write4(10);
					sendingClass.Write2(0);
					sendingClass.Write2(0);
					sendingClass.Write4(2359296);
					sendingClass.Write4(uint.MaxValue);
				}
				else
				{
					sendingClass.Write4(0);
					sendingClass.Write4(0);
					sendingClass.Write4(10);
					sendingClass.Write4(0);
					sendingClass.Write4(2359296);
					sendingClass.Write4(uint.MaxValue);
				}
			}
			Playe.Client?.SendPak(sendingClass, 26624, Playe.SessionID);
		}
		catch
		{
			LogHelper.WriteLine(LogLevel.Error, "Lỗi Update NPC DeXoa SoLieu 12345 !! ");
		}
	}

	public void UpdateNPCSoLieu(Players Playe)
	{
		try
		{
			using (SendingClass sendingClass = new())
			{
				sendingClass.Write4(1);
				sendingClass.Write4(NPC_SessionID);
				sendingClass.Write4(NPC_SessionID);
				sendingClass.Write2(FLD_PID);
				sendingClass.Write2(1);
				sendingClass.Write4(Rxjh_HP);
				sendingClass.Write4(Max_Rxjh_HP);
				sendingClass.Write(Rxjh_X);
				sendingClass.Write(Rxjh_Z);
				sendingClass.Write(Rxjh_Y);
				sendingClass.Write4(1082130432);
				sendingClass.Write(FLD_FACE1);
				sendingClass.Write(FLD_FACE2);
				sendingClass.Write(Rxjh_X);
				sendingClass.Write(Rxjh_Z);
				sendingClass.Write(Rxjh_Y);
				sendingClass.Write4(0);
				sendingClass.Write4(0);
				sendingClass.Write4(10);
				sendingClass.Write4(0);
				sendingClass.Write4(2359296);
				sendingClass.Write4(uint.MaxValue);
				Playe.Client?.SendPak(sendingClass, 26368, NPC_SessionID);
			}
			if (NPCDeath)
			{
				UpdateNPC_DeXoaSoLieu(Playe);
				LamMoi_NPCDeathSoLieu(Playe);
			}
		}
		catch
		{
			LogHelper.WriteLine(LogLevel.Error, "Lỗi Update NPC 11111 !! ");
		}
	}

	public void UpdateNPC_DeXoaSoLieu(Players Playe)
	{
		try
		{
			using SendingClass sendingClass = new();
			sendingClass.Write4(1);
			sendingClass.Write4(NPC_SessionID);
			sendingClass.Write4(NPC_SessionID);
			sendingClass.Write2(FLD_PID);
			sendingClass.Write2(1);
			sendingClass.Write4(Rxjh_HP);
			sendingClass.Write4(Max_Rxjh_HP);
			sendingClass.Write(Rxjh_X);
			sendingClass.Write(Rxjh_Z);
			sendingClass.Write(Rxjh_Y);
			sendingClass.Write4(1082130432);
			sendingClass.Write(FLD_FACE1);
			sendingClass.Write(FLD_FACE2);
			sendingClass.Write(Rxjh_X);
			sendingClass.Write(Rxjh_Z);
			sendingClass.Write(Rxjh_Y);
			sendingClass.Write4(0);
			sendingClass.Write4(0);
			sendingClass.Write4(10);
			sendingClass.Write4(0);
			sendingClass.Write4(2359296);
			sendingClass.Write4(uint.MaxValue);
			Playe.Client?.SendPak(sendingClass, 26624, Playe.SessionID);
		}
		catch
		{
			LogHelper.WriteLine(LogLevel.Error, "Lỗi Update NPC 2222 !! ");
		}
	}

	private void DoiMoi_NPC_HoiSinh_SoLieu()
	{
		var num = 0;
		try
		{
			num = 1;
			NPCDeath = false;
			Rxjh_HP = Max_Rxjh_HP;
			if (_FLD_PID != 15349 && _FLD_PID != 15350 && _FLD_PID != 15121 && _FLD_PID != 15122 && _FLD_PID != 16278 && _FLD_PID != 15293)
			{
				Random random = new((int)DateTime.Now.Ticks);
				var num2 = new Random(World.GetRandomSeed()).Next(0, 4);
				var num3 = random.NextDouble() * 50.0;
				var num4 = random.NextDouble() * 50.0;
				switch (num2)
				{
				case 0:
					Rxjh_X = Rxjh_cs_X + (float)num3;
					Rxjh_Y = Rxjh_cs_Y + (float)num4;
					break;
				case 1:
					Rxjh_X = Rxjh_cs_X - (float)num3;
					Rxjh_Y = Rxjh_cs_Y - (float)num4;
					break;
				case 2:
					Rxjh_X = Rxjh_cs_X + (float)num3;
					Rxjh_Y = Rxjh_cs_Y - (float)num4;
					break;
				default:
					Rxjh_X = Rxjh_cs_X - (float)num3;
					Rxjh_Y = Rxjh_cs_Y + (float)num4;
					break;
				}
			}
			else if (Rxjh_Map == 40101 && _FLD_PID == 16278)
			{
				Rxjh_X = RNG.Next(-250, 200);
				Rxjh_Y = 0f;
			}
			else
			{
				Rxjh_X = Rxjh_cs_X;
				Rxjh_Y = Rxjh_cs_Y;
			}
			Rxjh_Z = Rxjh_cs_Z;
			if (FLD_BOSS == 1)
			{
				foreach (var value in World.allConnectedChars.Values)
				{
					if (!value.Client.TreoMay)
					{
						value.HeThongNhacNho("Đại ma đầu vừa tái sinh tại bản đồ " + X_Toa_Do_Class.GetName_TiengViet(Rxjh_Map) + " - Tọa độ: [" + (int)Rxjh_X + "," + (int)Rxjh_Y + "]", 8, "Truyền Âm Các");
					}
				}
			}
			using (SendingClass sendingClass = new())
			{
				sendingClass.Write4(1);
				sendingClass.Write4(NPC_SessionID);
				sendingClass.Write4(NPC_SessionID);
				sendingClass.Write2(FLD_PID);
				sendingClass.Write2(1);
				sendingClass.Write4(Rxjh_HP);
				sendingClass.Write4(Max_Rxjh_HP);
				sendingClass.Write(Rxjh_X);
				sendingClass.Write(Rxjh_Z);
				sendingClass.Write(Rxjh_Y);
				sendingClass.Write4(0);
				sendingClass.Write(FLD_FACE1);
				sendingClass.Write(FLD_FACE2);
				sendingClass.Write(Rxjh_X);
				sendingClass.Write(Rxjh_Z);
				sendingClass.Write(Rxjh_Y);
				sendingClass.Write4(0);
				sendingClass.Write4(0);
				sendingClass.Write4(10);
				sendingClass.Write4(0);
				sendingClass.Write4(2359296);
				sendingClass.Write4(uint.MaxValue);
				SendCurrentRangeBroadcastData(sendingClass, 31488, NPC_SessionID);
			}
			if (TuDongHoiSinh != null)
			{
				TuDongHoiSinh.Enabled = false;
				TuDongHoiSinh.Close();
				TuDongHoiSinh.Dispose();
				TuDongHoiSinh = null;
			}
		}
		catch
		{
			num = 6;
			if (TuDongHoiSinh != null)
			{
				num = 7;
				TuDongHoiSinh.Close();
				num = 8;
				TuDongHoiSinh.Dispose();
				num = 9;
				TuDongHoiSinh = null;
			}
			num = 10;
			TuDongHoiSinh.Close();
			num = 11;
			TuDongHoiSinh.Dispose();
			num = 12;
			TuDongHoiSinh = null;
			LogHelper.WriteLine(LogLevel.Error, "NPC hồi sinh - Lỗi num: [" + num + "] -ID:[" + FLD_PID + "]");
		}
		finally
		{
			if (TuDongHoiSinh != null)
			{
				TuDongHoiSinh.Close();
				TuDongHoiSinh.Dispose();
				TuDongHoiSinh = null;
			}
		}
	}

	public static void Update_NPC_HoiSinh_SoLieu_FireDragon(Dictionary<int, NpcClass> NpcList, Players Playe)
	{
		if (NpcList == null || NpcList.Count <= 0)
		{
			return;
		}
		foreach (var value in NpcList.Values)
		{
			using SendingClass sendingClass = new();
			sendingClass.Write4(1);
			sendingClass.Write4(value.NPC_SessionID);
			sendingClass.Write4(value.NPC_SessionID);
			sendingClass.Write2(value.FLD_PID);
			sendingClass.Write2(1);
			sendingClass.Write4(value.Rxjh_HP);
			sendingClass.Write4(value.Max_Rxjh_HP);
			sendingClass.Write(value.Rxjh_X);
			sendingClass.Write(value.Rxjh_Z);
			sendingClass.Write(value.Rxjh_Y);
			sendingClass.Write(4f);
			sendingClass.Write(value.FLD_FACE1);
			sendingClass.Write(value.FLD_FACE2);
			sendingClass.Write(value.Rxjh_X);
			sendingClass.Write(value.Rxjh_Z);
			sendingClass.Write(value.Rxjh_Y);
			sendingClass.Write4(0);
			sendingClass.Write4(128369664);
			sendingClass.Write4(0);
			sendingClass.Write4(215040);
			sendingClass.Write4(0);
			sendingClass.Write4(786432);
			sendingClass.Write4(uint.MaxValue);
			Playe.Client?.SendPak(sendingClass, 31488, value.NPC_SessionID);
		}
	}

	private void LamMoi_NPCDeathSoLieu(Players Playe)
	{
		using SendingClass pak = new();
		Playe.Client?.SendPak(pak, 34816, NPC_SessionID);
	}

	public void QuangBa_NPCDeathSoLieu()
	{
        LogHelper.WriteLine(LogLevel.Info, "QuangBa_NPCDeathSoLieu");
		using SendingClass pak = new();
		SendCurrentRangeBroadcastData(pak, 34816, NPC_SessionID);
	}



	public void GuiDiTruocMatPhamViQuangBaSoLieu(byte[] Packet_DuLieu)
	{
		try
		{
			foreach (var value in World.allConnectedChars.Values)
			{
				if (value.NhanVatToaDo_BanDo == Rxjh_Map && FindPlayers(700, value) && value.Client != null && !value.Client.TreoMay)
				{
					value.Client.Send_Map_Data(Packet_DuLieu, Packet_DuLieu.Length);
				}
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Gửi đi trước mắt phạm vi qua\u0309ng bá số liệu 3 Phạm sai lầm:" + ex.StackTrace);
		}
	}

	public void GuiDi_DiDongSoLieu(float x, float y, int sl, int Type_Move, float oldx = -99999f, float oldy = -99999f)
	{
		if (World.jlMsg == 1)
		{
			LogHelper.WriteLine(0, "NpcClass_GuiDi DiDongSo Lieu");
		}
		try
		{
			SendingClass sendingClass = new();
			if (FLD_PID != 15293 && FLD_PID != 16270 && FLD_PID != 16271 && FLD_PID != 16430 && FLD_PID != 16430 && FLD_PID != 16435)
			{
				var posX = Rxjh_X;
				var posY = Rxjh_Y;
				if (oldx != -99999f && oldy != -99999f)
				{
					posX = oldx;
					posY = oldy;
				}
				double num3 = (int)Math.Sqrt((Rxjh_cs_X - (double)posX) * (Rxjh_cs_X - (double)posX) + (Rxjh_cs_Y - (double)posY) * (Rxjh_cs_Y - (double)posY));
				Random random = new(World.GetRandomSeed());
				var num4 = random.Next(0, 4);
				var num5 = random.NextDouble() * sl;
				var num6 = random.NextDouble() * sl;
				switch (num4)
				{
				case 0:
					Rxjh_X = x + (float)num5;
					Rxjh_Y = y + (float)num6;
					break;
				case 1:
					Rxjh_X = x - (float)num5;
					Rxjh_Y = y + (float)num6;
					break;
				case 2:
					Rxjh_X = x + (float)num5;
					Rxjh_Y = y - (float)num6;
					break;
				default:
					Rxjh_X = x - (float)num5;
					Rxjh_Y = y - (float)num6;
					break;
				}
				if (num3 > 100.0)
				{
					Rxjh_X = Rxjh_cs_X;
					Rxjh_Y = Rxjh_cs_Y;
				}
				sendingClass.Write(Rxjh_X);
				sendingClass.Write(Rxjh_Y);
				sendingClass.Write(Rxjh_Z);
				sendingClass.Write4(-1);
				sendingClass.Write4(Type_Move);
				sendingClass.Write((float)num5);
				sendingClass.Write4(Rxjh_HP);
				if (World.Item_Db_Byte_Length >= 15.0)
				{
					sendingClass.Write(posX);
					sendingClass.Write(Rxjh_Z);
					sendingClass.Write(posY);
					sendingClass.Write4(0);
					sendingClass.Write2(0);
				}
				SendCurrentRangeBroadcastData(sendingClass, 29696, NPC_SessionID);
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "NpcClass GuiDi DiDong SoLieu New Error" + FLD_PID + "|" + Name + " " + ex.Message);
		}
	}

	public void GuiDi_DiDongSoLieu_Old(float X, float Y, int sl, int DiDong_PhuongThuc)
	{
		try
		{
			var num = Rxjh_cs_X - Rxjh_X;
			var num2 = Rxjh_cs_Y - Rxjh_Y;
			double num3 = (int)Math.Sqrt(num * (double)num + num2 * (double)num2);
			using SendingClass sendingClass = new();
			if (FLD_PID != 15293 && FLD_PID != 16270 && FLD_PID != 16271 && FLD_PID != 16430 && FLD_PID != 16430 && FLD_PID != 16435)
			{
				Random random = new(DateTime.Now.Millisecond);
				var num4 = RNG.Next(0, 1);
				var num5 = random.NextDouble() * sl;
				var num6 = random.NextDouble() * sl;
				if (num4 == 0)
				{
					Rxjh_X = X + (float)num5;
					Rxjh_Y = Y + (float)num6;
				}
				else
				{
					Rxjh_X = X - (float)num5;
					Rxjh_Y = Y - (float)num6;
				}
				if (num3 > 200.0)
				{
					Rxjh_X = Rxjh_cs_X;
					Rxjh_Y = Rxjh_cs_Y;
				}
				sendingClass.Write(Rxjh_X);
				sendingClass.Write(Rxjh_Y);
				sendingClass.Write(Rxjh_Z);
				sendingClass.Write4(-1);
				if (FLD_PID == 5)
				{
					sendingClass.Write4(0);
				}
				else
				{
					sendingClass.Write4(RNG.Next(0, 2));
				}
				sendingClass.Write((float)num5);
				sendingClass.Write4(Rxjh_HP);
				sendingClass.Write(Rxjh_X);
				sendingClass.Write(Rxjh_Z);
				sendingClass.Write(Rxjh_Y);
				sendingClass.Write4(0);
				sendingClass.Write2(0);
				SendCurrentRangeBroadcastData(sendingClass, 29696, NPC_SessionID);
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "GuiDi DiDong SoLieu error: " + FLD_PID + "|" + Name + "   " + ex.Message);
		}
	}

	public void GuiDi_CongKichSoLieu(int CongKichLuc, int CongKichLoaiHinh, int CharacterFullServerID, int KhoiPhuc_LaChan)
	{
		try
		{
			using SendingClass sendingClass = new();
			sendingClass.Write4(CharacterFullServerID);
			sendingClass.Write2(1);
			sendingClass.Write2(0);
			sendingClass.Write4(CongKichLuc);
			sendingClass.Write4(19356438);
			sendingClass.Write4(0);
			sendingClass.Write4(0);
			sendingClass.Write4(0);
			sendingClass.Write4(KhoiPhuc_LaChan);
			sendingClass.Write4(0);
			sendingClass.Write4(0);
			sendingClass.Write4(0);
			sendingClass.Write4(0);
			sendingClass.Write4(0);
			sendingClass.Write4(CongKichLoaiHinh);
			sendingClass.Write(Rxjh_X);
			sendingClass.Write(15f);
			sendingClass.Write(Rxjh_Y);
			sendingClass.Write(0);
			sendingClass.Write(1);
			sendingClass.Write2(0);
			sendingClass.Write4(0);
			sendingClass.Write4(0);
			sendingClass.Write4(0);
			sendingClass.Write4(0);
			sendingClass.Write4(0);
			sendingClass.Write4(0);
			sendingClass.Write4(-1);
			sendingClass.Write4(0);
			sendingClass.Write4(0);
			sendingClass.Write4(0);
			sendingClass.Write4(0);
			sendingClass.Write4(0);
			sendingClass.Write4(0);
			sendingClass.Write4(0);
			sendingClass.Write4(0);
			sendingClass.Write4(0);
			sendingClass.Write4(0);
			SendCurrentRangeBroadcastData(sendingClass, 3072, NPC_SessionID);
		}
		catch (Exception ex)
		{
			var array = new string[6]
			{
				"Send CongKichSoLieu   error",
				FLD_PID.ToString(),
				"|",
				Name,
				"   ",
				null
			};
			array[5] = ex.ToString();
			LogHelper.WriteLine(LogLevel.Error, string.Concat(array));
		}
	}

	public void SendCurrentRangeBroadcastData(SendingClass pak, int id, int wordid, bool skipBs = false)
	{
		try
		{
			if (this.CurrentZone != null && this.CurrentZone.IsCrossServer && skipBs)
			{
				// Nếu là zone liên server, gọi phương thức mở rộng
				SendCurrentRangeBroadcastDataCrossServer(pak, id, wordid);
				//return;
			}
			if (PlayList == null)
			{
				return;
			}
			foreach (var value in PlayList.Values)
			{
				if (value.Client != null && value.Client.Running)
				{
					if (!value.Client.TreoMay)
					{
						value.Client.SendPak(pak, id, wordid);
					}
					continue;
				}
				if (Contains(value))
				{
					PlayList.Remove(value.SessionID);
				}
				if (value.NpcList != null && value.NpcList.Count > 0)
				{
					foreach (var value2 in value.NpcList.Values)
					{
						if (value2.Contains(value))
						{
							value2.PlayList.Remove(value.SessionID);
						}
					}
				}
				if (value.NpcList != null && value.Client != null)
				{
					value.Client.Dispose();
				}
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "NPC Qua\u0309ng bá số liệu | Phạm sai lầm 3：" + ex.Message);
		}
	}

	public void LamMoiTuVongSoLieu()
	{
		try
		{
			if (NPCDeath)
			{
				return;
			}
			NPCDeath = true;
			if (_QuaiXuatHien_DuyNhatMotLan)
			{
				if (PlayCw != null)
				{
					PlayCw = null;
				}
				Play_null();
				QuangBa_NPCDeathSoLieu();
				Dispose();
				return;
			}
			if (AutomaticAttack != null)
			{
				AutomaticAttack.Enabled = false;
			}
			if (AutomaticMove != null)
			{
				AutomaticMove.Enabled = false;
			}
			if (TuDongHoiSinh != null)
			{
				TuDongHoiSinh.Interval = FLD_NEWTIME * 1000;
				TuDongHoiSinh.Enabled = true;
			}
			else
			{
				TuDongHoiSinh = new(FLD_NEWTIME * 1000);
				TuDongHoiSinh.Elapsed += TuDongHoiSinhEvent;
				TuDongHoiSinh.Enabled = true;
			}
			if (PlayCw != null)
			{
				PlayCw = null;
			}
			Play_null();
			QuangBa_NPCDeathSoLieu();
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "LamMoi TuVong SoLieu error [" + FLD_PID + "]-Name:[" + Name + "] - " + ex.Message);
		}
	}

	public void GuiDiHaiThuocSoLieu()
	{
		if (IsNpc == 2)
		{
			if (TuDongHoiSinh != null)
			{
				TuDongHoiSinh.Interval = FLD_NEWTIME * 1000;
				TuDongHoiSinh.Enabled = true;
			}
			else
			{
				TuDongHoiSinh = new(FLD_NEWTIME * 1000);
				TuDongHoiSinh.Elapsed += TuDongHoiSinhEvent;
				TuDongHoiSinh.Enabled = true;
			}
			PlayCw = null;
			Play_null();
			QuangBa_NPCDeathSoLieu();
		}
	}

	public void GuiDuLieu_TuVong_MotLanCuaQuaiVat()
	{
		try
		{
			AbnormalStatusList();
			EndAbnormalBloodDropStatusList();
			if (IsNpc != 1 && !NPCDeath)
			{
				if (PlayCw != null)
				{
					PlayCw = null;
				}
				Play_null();
				QuangBa_NPCDeathSoLieu();
				Dispose();
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "GuiDi TuVong SoLieu lỗi 222 | " + FLD_PID + "|" + Name + "   " + ex.Message);
		}
	}
	public void GuiDiTuVongSoLieu()
    {
        try
        {
			
            AbnormalStatusList();
            EndAbnormalBloodDropStatusList();

            if(IsNpc == 1)
                return;
            QuangBa_NPCDeathSoLieu();
            Task.Delay(2000).ContinueWith(_ =>
            {
                PlayList?.Clear();
                PlayerTargetList?.Clear();
                NPCDeath = true;
                timeNpcRevival = DateTime.Now.AddSeconds(FLD_NEWTIME);

                if(QuaiXuatHien_DuyNhatMotLan)
                {

                    if(PlayCw != null)
                        PlayCw = null;
                    timeNpcRevival = DateTime.MinValue;
                    Play_null();
                    Dispose();
                    return;
                }

                    if(AutomaticAttack != null)
                        AutomaticAttack.Enabled = false;
                    if(AutomaticMove != null)
                        AutomaticMove.Enabled = true;

                if(TuDongHoiSinh != null)
                {
                    TuDongHoiSinh.Interval = FLD_NEWTIME * 1000;
                    TuDongHoiSinh.Enabled = true;
                }
                else
                {
                    TuDongHoiSinh = new System.Timers.Timer(FLD_NEWTIME * 1000);
                    TuDongHoiSinh.Elapsed += TuDongHoiSinhEvent;
                    TuDongHoiSinh.Enabled = true;
                }

                if(PlayCw != null)
                    PlayCw = null;
                Play_null();
            });
        }
        catch(Exception ex2)
        {
            Play_null();
            QuangBa_NPCDeathSoLieu();
            if(QuaiXuatHien_DuyNhatMotLan)
                timeNpcRevival = DateTime.MinValue;
            else
                timeNpcRevival = DateTime.Now.AddSeconds(FLD_NEWTIME);
            LogHelper.WriteLine(LogLevel.Error, "GuiDiTuVongSoLieu   error" + FLD_PID + "|" + Name + "   " + ex2.Message);
        }
    }

	public void GuiDiTuVongSoLieu(int SessionId)
	{
		var num = 0;
		if (PlayList == null || !PlayList.TryGetValue(SessionId, out var attacker))
		{
			return;
		}
		var num2 = 0;
		try
		{
			foreach (var value9 in attacker.QuestList.Values)
			{
				var dictionary = MapClass.GetnpcTemplate(SessionId);
				foreach (var item in value9.NhiemVu_GiaiDoan)
				{
					if ((item.GiaiDoan_TrangThai != 0 && item.GiaiDoan_TrangThai != 1) || item.GiaiDoanCanVatPham_.Count <= 0)
					{
						continue;
					}
					var num3 = 0;
					var rW = new X_Nhiem_Vu_Loai().GetRW(value9.RwID);
					foreach (var item2 in rW.NhiemVu_GiaiDoan)
					{
						if (item2.GiaiDoanID == item.GiaiDoanID)
						{
							num3 = item2.MucDo_KhoKhan;
							item.GiaiDoanCanVatPham_ = item2.GiaiDoanCanVatPham_;
							break;
						}
					}
					num = value9.RwID;
					if (num == 45 && 15062 == FLD_PID)
					{
						attacker.SetUpQuestItems(900000099, 1);
					}
					if (num == 46 && 15072 == FLD_PID)
					{
						attacker.SetUpQuestItems(900000101, 1);
					}
					var key = RNG.Next(0, item.GiaiDoanCanVatPham_.Count - 1);
					var x_Giai_Doan_Can_Vat_Pham_Loai = item.GiaiDoanCanVatPham_[key];
					var num4 = 0;
					if (value9.NhiemVuID == 1201)
					{
						num4 = RNG.Next(1, World.Rate_Rot_Quest);
					}
					else if (value9.NhiemVuID == 1202)
					{
						num4 = RNG.Next(1, 200);
					}
					else if (value9.NhiemVuID == 1203)
					{
						num4 = RNG.Next(1, 70);
					}
					else if (value9.NhiemVuID == 1204)
					{
						num4 = RNG.Next(1, 100);
					}
					if (num4 > num3)
					{
						continue;
					}
					if (attacker.TeamID != 0 && World.WToDoi.TryGetValue(attacker.TeamID, out var value2) && value9.NhiemVuID != 1203)
					{
						foreach (var value10 in value2.ToDoi_NguoiChoi.Values)
						{
							if ((value10.CompletedQuestList.TryGetValue(1201, out var value3) && value3.nhiemvudate.Date == DateTime.Now.Date) || !FindPlayers(800, value10) || value10.NhanVat_HP <= 0 || value10.PlayerTuVong || attacker.SessionID == value10.SessionID)
							{
								continue;
							}
							foreach (var value11 in value10.QuestList.Values)
							{
								if (value11.NhiemVuID != value9.NhiemVuID)
								{
									continue;
								}
								foreach (var item3 in value11.NhiemVu_GiaiDoan)
								{
									if (item3.GiaiDoanCanVatPham_.Count <= 0)
									{
										continue;
									}
									foreach (var item4 in rW.NhiemVu_GiaiDoan)
									{
										if (item4.GiaiDoanID == item3.GiaiDoanID)
										{
											item3.GiaiDoanCanVatPham_ = item4.GiaiDoanCanVatPham_;
											break;
										}
									}
									var x_Giai_Doan_Can_Vat_Pham_Loai2 = item3.GiaiDoanCanVatPham_[key];
									if (x_Giai_Doan_Can_Vat_Pham_Loai2.QuaiVat_ID == FLD_PID && !value10.CheckItem(x_Giai_Doan_Can_Vat_Pham_Loai2.VatPham_ID, x_Giai_Doan_Can_Vat_Pham_Loai2.TongSoVatPham))
									{
										if (value9.NhiemVuID == 1201 && value11.NhiemVuID == value9.NhiemVuID)
										{
											value10.SetUpQuestItems(World.Item_Quest, 1);
										}
										else
										{
											value10.SetUpQuestItems(x_Giai_Doan_Can_Vat_Pham_Loai2.VatPham_ID, 1);
										}
									}
								}
							}
						}
						if (x_Giai_Doan_Can_Vat_Pham_Loai.QuaiVat_ID != FLD_PID || attacker.CheckItem(x_Giai_Doan_Can_Vat_Pham_Loai.VatPham_ID, x_Giai_Doan_Can_Vat_Pham_Loai.TongSoVatPham))
						{
							continue;
						}
						if (attacker.CompletedQuestList.TryGetValue(1201, out var value4))
						{
							if (value4.nhiemvudate.Date != DateTime.Now.Date)
							{
								if (value9.NhiemVuID == 1201)
								{
									attacker.SetUpQuestItems(World.Item_Quest, 1);
								}
								else
								{
									attacker.SetUpQuestItems(x_Giai_Doan_Can_Vat_Pham_Loai.VatPham_ID, 1);
								}
							}
						}
						else if (value9.NhiemVuID == 1201)
						{
							attacker.SetUpQuestItems(World.Item_Quest, 1);
						}
						else
						{
							attacker.SetUpQuestItems(x_Giai_Doan_Can_Vat_Pham_Loai.VatPham_ID, 1);
						}
					}
					else
					{
						if (x_Giai_Doan_Can_Vat_Pham_Loai.QuaiVat_ID != FLD_PID || attacker.CheckItem(x_Giai_Doan_Can_Vat_Pham_Loai.VatPham_ID, x_Giai_Doan_Can_Vat_Pham_Loai.TongSoVatPham))
						{
							continue;
						}
						if (value9.NhiemVuID == 1201)
						{
							attacker.SetUpQuestItems(World.Item_Quest, 1);
						}
						else if (value9.NhiemVuID == 1203 && World.Event_Noel_Progress != 0)
						{
							attacker.SetUpQuestItems(x_Giai_Doan_Can_Vat_Pham_Loai.VatPham_ID, 1);
						}
						else if (x_Giai_Doan_Can_Vat_Pham_Loai.QuaiVat_ID == 15900)
						{
							if (World.Event_Noel_Progress != 0)
							{
								attacker.SetUpQuestItems(x_Giai_Doan_Can_Vat_Pham_Loai.VatPham_ID, 1);
							}
						}
						else
						{
							attacker.SetUpQuestItems(x_Giai_Doan_Can_Vat_Pham_Loai.VatPham_ID, 1);
						}
					}
				}
			}
			EndAbnormalBloodDropStatusList();
			if (IsNpc == 1 || NPCDeath)
			{
				return;
			}
			if (World.NpcEvent_DCH.TryGetValue(NPC_SessionID, out var value5))
			{
				if (World.allConnectedChars.TryGetValue(SessionId, out attacker) && attacker.NhanVatToaDo_BanDo == 40101)
				{
					Boss_BaoSuat_VatPham_DCH(attacker);
					if (value5.FLD_PID >= 16272 && value5.FLD_PID <= 16273)
					{
						attacker.DCH_StackA_SoLuong += 4;
						attacker.DCH_Stack_Add(attacker, 1008001797, attacker.DCH_StackA_SoLuong, 1);
						attacker.DCH_StackA = 100;
					}
					else if (value5.FLD_PID >= 16274 && value5.FLD_PID <= 16277)
					{
						attacker.DCH_StackA_SoLuong += 4;
						attacker.DCH_Stack_Add(attacker, 1008001797, attacker.DCH_StackA_SoLuong, 1);
						attacker.DCH_StackB_SoLuong += 2;
						attacker.DCH_StackA = 100;
						attacker.DCH_Stack_Add(attacker, 1008001798, attacker.DCH_StackB_SoLuong, 1);
						attacker.DCH_StackB = 400;
					}
					else if (value5.FLD_PID == 16278)
					{
						attacker.DCH_StackC_SoLuong += 10;
						attacker.DCH_Stack_Add(attacker, 1008001799, attacker.DCH_StackC_SoLuong, 1);
						attacker.DCH_StackC = 4000;
						attacker.UpdateCharacterData(attacker);
						attacker.UpdateBroadcastCharacterData();
						foreach (var value12 in World.allConnectedChars.Values)
						{
							value12.HeThongNhacNho("[" + attacker.CharacterName + "] hạ sát đại ma đầu Diêm La Quân, chiến công lừng lẫy!", 21, "Thiên cơ các");
							value12.HeThongNhacNho("[" + attacker.CharacterName + "] hạ sát đại ma đầu Diêm La Quân, danh vang tứ hải!", 22, "Thiên cơ các");
							value12.HeThongNhacNho("[" + attacker.CharacterName + "] hạ sát đại ma đầu Diêm La Quân, anh hùng vô song!", 23, "Thiên cơ các");
						}
					}
				}
			}
			else if (FLD_BOSS == 1)
			{
				// if (World.CauHinhBossTheoDame.Contains(FLD_PID.ToString()))
				// {
				// 	var playGjClass = FindMaxDame(PlayerTargetList);
				// 	var playGjTeamClass = FindMaxDameTeam(PlayerTargetList);
				// 	if (playGjClass.Gjxl > playGjTeamClass.Gjxl_team)
				// 	{
				// 		if (World.allConnectedChars.TryGetValue(playGjClass.PlayID, out attacker))
				// 		{
				// 			if (World.CoHayKo_BOSS_Rot_Item == 1)
				// 			{
				// 				Boss_BaoSuat_VatPham(attacker);
				// 			}
				// 			else
				// 			{
				// 				attacker.HeThongNhacNho("Kênh này được Thiên Cơ Các phong ấn, không rơi bảo vật từ đại ma đầu!!!", 20, "Thiên cơ các");
				// 			}
				// 		}
				// 		else
				// 		{
				// 			var text = "Loai 111: [" + NPC_SessionID + "][" + FLD_PID + "][" + SessionId + "]";
				// 			// logo.Log_Drop_BOSS_loi(text, attacker.UserName);
				// 			LogHelper.WriteLine(LogLevel.Error, "Lỗi drop BOSS tại đây 111 !! - " + text);
				// 		}
				// 	}
				// 	else
				// 	{
				// 		World.WToDoi.TryGetValue(playGjTeamClass.TeamID, out var value6);
				// 		if (value6 != null)
				// 		{
				// 			var characterFullServerID = value6.DoiTruong.SessionID;
				// 			if (World.allConnectedChars.TryGetValue(characterFullServerID, out attacker))
				// 			{
				// 				if (World.CoHayKo_BOSS_Rot_Item == 1)
				// 				{
				// 					Boss_BaoSuat_VatPham(attacker);
				// 				}
				// 				else
				// 				{
				// 					attacker.HeThongNhacNho("Kênh này được Thiên Cơ Các phong ấn, không rơi bảo vật từ đại ma đầu!!!", 20, "Thiên cơ các");
				// 				}
				// 			}
				// 			else
				// 			{
				// 				var text2 = "Loai 222: [" + NPC_SessionID + "][" + FLD_PID + "][" + SessionId + "]";
				// 				// logo.Log_Drop_BOSS_loi(text2, attacker.UserName);
				// 				LogHelper.WriteLine(LogLevel.Error, "Lỗi drop BOSS tại đây 222 !! - " + text2);
				// 			}
				// 		}
				// 		else
				// 		{
				// 			var text3 = "Loai 111 222: [" + NPC_SessionID + "][" + FLD_PID + "][" + SessionId + "]";
				// 			// logo.Log_Drop_BOSS_loi(text3, attacker.UserName);
				// 			LogHelper.WriteLine(LogLevel.Error, "Lỗi drop BOSS tại đây 222 !! - " + text3);
				// 		}
				// 	}
				// }
				// else if (World.allConnectedChars.TryGetValue(SessionId, out attacker))
				// {
				// 	if (World.CoHayKo_BOSS_Rot_Item == 1)
				// 	{
				// 		Boss_BaoSuat_VatPham(attacker);
				// 		if (!attacker.Client.TreoMay)
				// 		{
				// 			var text4 = "Đại hiệp [" + attacker.UserName + "] đã tung hoành giang hồ, hạ gục [" + Name + "] . Trận chiến oanh liệt diễn ra ở [Kênh " + World.ServerID + "], ngay tại [" + X_Toa_Do_Class.getmapname(attacker.NhanVatToaDo_BanDo) + "] ở tọa độ [" + (int)attacker.NhanVatToaDo_X + "," + (int)attacker.NhanVatToaDo_Y + "]. Giang hồ đồn đại, ai dám khiêu chiến tiếp theo?";

				// 			World.conn.Transmit("PK_MESSAGE|" + 22 + "|" + text4);
				// 		}
				// 	}
				// 	else
				// 	{
				// 		attacker.HeThongNhacNho("Kênh này được Thiên Cơ Các phong ấn, không rơi bảo vật từ đại ma đầu!!!", 20, "Thiên cơ các");
				// 	}
				// }
				// else
				// {
				// 	var text5 = "Loai 333: [" + NPC_SessionID + "][" + FLD_PID + "][" + SessionId + "]";
				// 	// logo.Log_Drop_BOSS_loi(text5, attacker.UserName);
				// 	LogHelper.WriteLine(LogLevel.Error, "Lỗi drop BOSS tại đây 333 !! - " + text5);
				// }
			}
			else if (PlayList != null && PlayList.TryGetValue(SessionId, out attacker))
			{
				if (attacker.Player_Level - Level < World.LayDuoc_KinhNghiem_CapDo_ChenhLech)
				{
					BaoSuat_VatPham(attacker);
				}
				if (World.Event_Noel_Progress != 0)
				{
					if (FLD_PID == World.ID_Monster_Drop_Event_GiangSinh)
					{
						var num5 = RNG.Next(1, 100);
						if (num5 >= 90)
						{
							attacker.DROP_ITEM_ADD(World.PhanThuong_Drop_Event_GiangSinh, 0, 0, 0, 0, 0, "", attacker);
							foreach (var value13 in World.allConnectedChars.Values)
							{
								if (attacker.SessionID != value13.SessionID)
								{
									value13.HeThongNhacNho("Đánh bại Tuần Lộc, bảo vật hộp quà rơi ra khắp nơi!", RNG.Next(21, 23), "[" + attacker.CharacterName + "]");
								}
								else
								{
									attacker.HeThongNhacNho("Đại hiệp đánh bại Tuần Lộc, hộp quà rơi đầy đất!", 7, "Giáng Sinh");
								}
							}
						}
					}
				}
				else if (World.Event_Tet_GiapThin_Progress != 0)
				{
					//if (FLD_PID == World.ID_Monster_Drop_Event_TetGiapThin && World.Event_Tet_GiapThin_Progress != 0)
					//{
					//	Random random = new();
					//	var num6 = random.Next(1, 100);
					//	if (num6 <= 30)
					//	{
					//		attacker.DROP_ITEM_ADD(World.PhanThuong_Drop_Event_TetGiapThin, 0, 0, 0, 0, 0, "", attacker);
					//		attacker.HeThongNhacNho("Hộp Nhiệm Vụ Tết rơi ra từ Dưa Hấu, giang hồ náo nhiệt!!", 7, "Dưa Hấu");
					//	}
					//	else if (num6 <= 70)
					//	{
					//		var num7 = random.Next(1, 100);
					//		if (num7 <= 30)
					//		{
					//			attacker.DROP_ITEM_ADD(1000000001, 0, 0, 0, 0, 0, "", attacker);
					//			attacker.HeThongNhacNho("Hộp NHT rơi ra từ Dưa Hấu, quần hùng tranh đoạt!!", 7, "Dưa Hấu");
					//		}
					//		else if (num7 <= 35)
					//		{
					//			switch (random.Next(1, 9))
					//			{
					//			case 1:
					//				attacker.DROP_ITEM_ADD(700114, 0, 70000005, 0, 0, 0, "", attacker);
					//				attacker.HeThongNhacNho("Nhẫn Võ Công rơi ra từ Dưa Hấu, bảo vật hiếm có!!", 7, "Dưa Hấu");
					//				break;
					//			case 2:
					//				attacker.DROP_ITEM_ADD(109, 0, 80000003, 0, 0, 0, "", attacker);
					//				attacker.HeThongNhacNho("Khuyên Cửu Thiên rơi ra từ Dưa Hấu, thần khí lẫy lừng!!", 7, "Dưa Hấu");
					//				break;
					//			case 3:
					//				attacker.DROP_ITEM_ADD(111, 0, 30000200, 0, 0, 0, "", attacker);
					//				attacker.HeThongNhacNho("Khuyên Hồng Ngọc rơi ra từ Dưa Hấu, bảo châu rực rỡ!!", 7, "Dưa Hấu");
					//				break;
					//			case 4:
					//				attacker.DROP_ITEM_ADD(700116, 0, 70000007, 40000050, 0, 0, "", attacker);
					//				attacker.HeThongNhacNho("Nhẫn Ám Ảnh rơi ra từ Dưa Hấu, khí tức huyền bí!!", 7, "Dưa Hấu");
					//				break;
					//			case 5:
					//				attacker.DROP_ITEM_ADD(110, 0, 80000003, 70000002, 0, 0, "", attacker);
					//				attacker.HeThongNhacNho("Khuyên Hoàng Ngọc rơi ra từ Dưa Hấu, ngọc quý giang hồ!!", 7, "Dưa Hấu");
					//				break;
					//			case 6:
					//				attacker.DROP_ITEM_ADD(100114, 0, 20000025, 0, 0, 0, "", attacker);
					//				attacker.HeThongNhacNho("Dây Chuyền Ám rơi ra từ Dưa Hấu, bảo vật ma mị!!", 7, "Dưa Hấu");
					//				break;
					//			case 7:
					//				attacker.DROP_ITEM_ADD(700117, 0, 10000020, 70000003, 0, 0, "", attacker);
					//				attacker.HeThongNhacNho("Nhẫn Bá Lực rơi ra từ Dưa Hấu, sức mạnh vô song!!", 7, "Dưa Hấu");
					//				break;
					//			case 8:
					//				attacker.DROP_ITEM_ADD(100117, 0, 20000030, 60000030, 0, *********, "", attacker);
					//				attacker.HeThongNhacNho("Dây Chuyền Bá Hoàng rơi ra từ Dưa Hấu, uy danh hiển hách!!", 7, "Dưa Hấu");
					//				if (!attacker.Client.TreoMay)
					//				{
					//					var text6 = $"[{attacker.UserName}] nhat duoc [Day Chuyen Ba Hoang] Event Dua Hau, tai [Kênh {World.ServerID}] map [{X_Toa_Do_Class.getmapname(attacker.NhanVatToaDo_BanDo)}] [{Rxjh_X},{Rxjh_Y}]";
					//					World.conn.Transmit("PK_MESSAGE|" + 10 + "|" + text6);
					//				}
					//				break;
					//			}
					//		}
					//		else if (num7 <= 50)
					//		{
					//			attacker.DROP_ITEM_ADD(1000000286, 0, 0, 0, 0, 0, "", attacker);
					//			attacker.HeThongNhacNho("Thẻ Võ Huân 100 rơi ra từ Dưa Hấu, chiến tích nhỏ mà quý!!", 7, "Dưa Hấu");
					//		}
					//		else if (num7 <= 60)
					//		{
					//			attacker.DROP_ITEM_ADD(1000000287, 0, 0, 0, 0, 0, "", attacker);
					//			attacker.HeThongNhacNho("Thẻ Võ Huân 200 rơi ra từ Dưa Hấu, công lao đáng kể!!", 7, "Dưa Hấu");
					//		}
					//		else if (num7 <= 62)
					//		{
					//			attacker.DROP_ITEM_ADD(1000000288, 0, 0, 0, 0, 0, "", attacker);
					//			attacker.HeThongNhacNho("Thẻ Võ Huân 1000 rơi ra từ Dưa Hấu, danh vọng lẫy lừng!!", 7, "Dưa Hấu");
					//			if (!attacker.Client.TreoMay)
					//			{
					//				var text7 = $"[{attacker.UserName}] nhat duoc [The Vo Huan 1000] Event Dua Hau, tai [Kênh {World.ServerID}] map [{X_Toa_Do_Class.getmapname(attacker.NhanVatToaDo_BanDo)}] [{Rxjh_X},{Rxjh_Y}]";
					//				World.conn.Transmit("PK_MESSAGE|" + 10 + "|" + text7);
					//			}
					//		}
					//		else if (num7 <= 65)
					//		{
					//			attacker.DROP_ITEM_ADD(1000000290, 0, 0, 0, 0, 0, "", attacker);
					//			attacker.HeThongNhacNho("Thẻ Võ Huân 500 rơi ra từ Dưa Hấu, chiến công hiển hách!!", 7, "Dưa Hấu");
					//		}
					//		else
					//		{
					//			attacker.DROP_ITEM_ADD(1000000009, 0, 0, 0, 0, 0, "", attacker);
					//			attacker.HeThongNhacNho("Hộp quà Tết Giáp Thìn rơi ra từ Dưa Hấu, phúc lộc đầy tay!!", 7, "Dưa Hấu");
					//			if (!attacker.Client.TreoMay)
					//			{
					//				var text8 = $"[{attacker.UserName}] nhat duoc [Hop qua Tet Giap Thin] Event Dua Hau, tai [Kênh {World.ServerID}] map [{X_Toa_Do_Class.getmapname(attacker.NhanVatToaDo_BanDo)}] [{Rxjh_X},{Rxjh_Y}]";
					//				World.conn.Transmit("PK_MESSAGE|" + 10 + "|" + text8);
					//			}
					//		}
					//	}
					//	else
					//	{
					//		attacker.DROP_ITEM_ADD(World.PhanThuong_Drop_Event_TetGiapThin_2, 0, 0, 0, 0, 0, "", attacker);
					//		attacker.HeThongNhacNho("Chỉ còn cái nịt, giang hồ cười vang!!", 23, "Dưa Hấu");
					//	}
					//}
				}
				else if (World.Event_TetDoanNgo_Progress != 0)
				{
					//if (FLD_PID == 15236 && FLD_BOSS == 0)
					//{
					//	var parcelVacancy = attacker.GetParcelVacancy(attacker);
					//	if (parcelVacancy != -1)
					//	{
					//		attacker.AddItem_ThuocTinh_int(1000000700, parcelVacancy, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
					//		attacker.HeThongNhacNho("Đại hiệp nhận được bảo vật từ sự kiện, thật đáng mừng!");
					//	}
					//	else
					//	{
					//		attacker.HeThongNhacNho("Hành trang không còn chỗ trống!", 10, "Thiên cơ các");
					//	}
					//}
				}
				else if (World.KhuLuyenTap_PK_Event != null && ((FLD_PID >= 15096 && FLD_PID <= 15099) || (FLD_PID >= 15172 && FLD_PID <= 15175) || (FLD_PID >= 15414 && FLD_PID <= 15417)) && RNG.Next(1, 100) <= 15.0)
				{
					// Phần thưởng PK Khu Luyện Tập
					//var parcelVacancy2 = attacker.GetParcelVacancy(attacker);
					//if (parcelVacancy2 == -1)
					//{
					//	attacker.HeThongNhacNho("Hành trang của đại hiệp đã đầy, không còn chỗ chứa thêm bảo vật!", 20, "Thiên cơ các");
					//	return;
					//}
					//double num8 = RNG.Next(1, 110);
					//string text9;
					//int value7;
					//if (num8 <= 30.0)
					//{
					//	text9 = "Bò Xanh Cánh Bướm";
					//	value7 = 1000001301;
					//}
					//else if (num8 <= 40.0)
					//{
					//	text9 = "Danh Hiệu Hiệp Khách (1)";
					//	value7 = 1000002051;
					//}
					//else if (num8 <= 50.0)
					//{
					//	text9 = "Hộp Địa Hạ Khí";
					//	value7 = 1008000085;
					//}
					//else if (num8 <= 60.0)
					//{
					//	text9 = "Hạt Ngọc (1)";
					//	value7 = 1900001;
					//}
					//else if (num8 <= 70.0)
					//{
					//	text9 = "Hộp Áo Choàng";
					//	value7 = 1000000272;
					//}
					//else if (num8 <= 80.0)
					//{
					//	text9 = "Hộp TB/VK 16x";
					//	value7 = 1000000531;
					//}
					//else if (num8 <= 90.0)
					//{
					//	text9 = "Linh Hồn Chân Khí";
					//	value7 = 900000841;
					//}
					//else
					//{
					//	text9 = "Huy Chương Thế Lực Chiến";
					//	value7 = *********;
					//}
					//attacker.IncreaseItem4(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(value7), parcelVacancy2, BitConverter.GetBytes(1), new byte[56]);
					//if (!attacker.Client.TreoMay)
					//{
					//	foreach (var value14 in World.allConnectedChars.Values)
					//	{
					//		value14.HeThongNhacNho("[" + attacker.UserName + "] nhặt được bảo vật [" + text9 + "] tại bản đồ [" + X_Toa_Do_Class.getmapname(attacker.NhanVatToaDo_BanDo) + "]", 23, "Thiên cơ các");
					//	}
					//}
				}
			}
			else
			{
				num2 = 6;
				Math.Abs(PlayCw.Playe.Player_Level - Level);
				if (SessionId > 40000 && PlayCw != null && PlayCw.Playe != null && PlayCw.Playe.Player_Level - Level < World.LayDuoc_KinhNghiem_CapDo_ChenhLech)
				{
					BaoSuat_VatPham(PlayCw.Playe);
				}
				else
				{
					BaoSuat_VatPham(null);
				}
			}
			foreach (var value15 in World.allConnectedChars.Values)
			{
				if (World.allConnectedChars.TryGetValue(SessionId, out var value8))
				{
					if (FLD_PID == 16430)
					{
						num2 = 9;
						value15.ThienMaThanCungCuaChinhThanhDaMoRa();
						World.ThienMaThanCungDaiMon_PhaiChangTuVong = 1;
					}
					if (FLD_PID == 16431)
					{
						num2 = 10;
						value15.ThienMaThanCungCuaThanhDong_DaMo();
						World.ThienMaThanCungDongMon_PhaiChangTuVong = 1;
					}

					if (FLD_PID != 16435) continue;
					num2 = 11;
					DBA.ExeSqlCommand("DELETE FROM CongThanhChien_ThanhChu").GetAwaiter().GetResult();
					value8.ThienMaThanCungPhoTuongKichSat();
					value15.ThienMaThanCungCongThanhChienThangPacket(value8);
					value15.ThienMaThanCungPhoTuongKichSat_DiDong(value15);
					World.ThienMaThanCungPhoTuong_PhaiChangTuVong = 1;
				}
			}
			NPCDeath = true;
			timeNpc_HoiSinh = DateTime.Now.AddSeconds(FLD_NEWTIME);
			if (_QuaiXuatHien_DuyNhatMotLan)
			{
				if (PlayCw != null)
				{
					PlayCw = null;
				}
				timeNpc_HoiSinh = DateTime.MinValue;
				Play_null();
				QuangBa_NPCDeathSoLieu();
				Dispose();
				return;
			}
			if (AutomaticAttack != null)
			{
				AutomaticAttack.Enabled = false;
			}
			if (AutomaticMove != null)
			{
				AutomaticMove.Enabled = true;
			}
			if ((World.eve != null || World.tmc_flag) && attacker.NhanVatToaDo_BanDo == 801 && attacker.Player_Zx == 1)
			{
				attacker.HeThongNhacNho("Hiệp khách [" + attacker.CharacterName + "] đã hạ sát [" + Name + "] tại bản đồ [" + X_Toa_Do_Class.getmapname(attacker.NhanVatToaDo_BanDo) + "]", 6, "Thiên cơ các");
				attacker.GuiDi_TheLucChien_KetThuc_TinTuc(1);
			}
			if ((World.eve != null || World.tmc_flag) && attacker.NhanVatToaDo_BanDo == 801 && attacker.Player_Zx == 2)
			{
				attacker.HeThongNhacNho("Hiệp khách [" + attacker.CharacterName + "] đã hạ sát [" + Name + "] tại bản đồ [" + X_Toa_Do_Class.getmapname(attacker.NhanVatToaDo_BanDo) + "]", 6, "Thiên cơ các");
				attacker.GuiDi_TheLucChien_KetThuc_TinTuc(2);
			}
			if (World.TheLucChien_Progress == 3 && (_FLD_PID == 16320 || _FLD_PID == 16321) && Rxjh_Map == 801)
			{
				if (_FLD_PID == 16320)
				{
					World.TheLucChien_TaPhai_DiemSo += 25;
				}
				else if (_FLD_PID == 16321)
				{
					World.TheLucChien_ChinhPhai_DiemSo += 25;
				}
			}
			if (TuDongHoiSinh != null)
			{
				TuDongHoiSinh.Interval = FLD_NEWTIME * 1000;
				TuDongHoiSinh.Enabled = true;
			}
			else
			{
				TuDongHoiSinh = new(FLD_NEWTIME * 1000);
				TuDongHoiSinh.Elapsed += TuDongHoiSinhEvent;
				TuDongHoiSinh.Enabled = true;
			}
			if (PlayCw != null)
			{
				PlayCw = null;
			}
			Play_null();
			QuangBa_NPCDeathSoLieu();
		}
		catch (Exception ex)
		{
			Play_null();
			QuangBa_NPCDeathSoLieu();
			if (QuaiXuatHien_DuyNhatMotLan)
			{
				timeNpc_HoiSinh = DateTime.MinValue;
			}
			else
			{
				timeNpc_HoiSinh = DateTime.Now.AddSeconds(FLD_NEWTIME);
			}
			LogHelper.WriteLine(LogLevel.Error, "Send TuVong SoLieu lỗi tại num: [" + num2 + "]-[" + FLD_PID + "]-[" + Name + "] - " + ex.Message);
		}
	}

	public void GuiDi_PhanSatThuong_CongKichSoLieu(int CongKichLuc, int NhanVat_ID)
	{
		var array = Converter.HexStringToByte("AA551200A42789000C002C0100000F0000000100000055AA");
		System.Buffer.BlockCopy(BitConverter.GetBytes(NPC_SessionID), 0, array, 4, 2);
		System.Buffer.BlockCopy(BitConverter.GetBytes(NhanVat_ID), 0, array, 10, 2);
		System.Buffer.BlockCopy(BitConverter.GetBytes(CongKichLuc), 0, array, 18, 2);
		QuangBaSoLieu(array, array.Length);
	}

	public byte[] RoiRaVatPham(DropClass drop, Players yxqname)
	{
		try
		{
			var dBItmeId = RxjhClass.CreateItemSeries();
			var array = new byte[World.Item_Db_Byte_Length];
			var bytes = BitConverter.GetBytes(dBItmeId);
			var array2 = new byte[56];
			var array3 = Converter.HexStringToByte("AA557200940223006400010000008716E567818320060208AF2F000000000100000000000000010F020F00020000470D0300000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000C3E755AA");
			if (!World.ItemList.TryGetValue(drop.FLD_PID, out var value))
			{
				return null;
			}
			if (value.FLD_QUESTITEM != 1)
			{
				try
				{
					if (World.Droplog)
					{
						LogHelper.WriteLine(LogLevel.Debug, "VatPham Làm rơi VatPhamTen [" + drop.FLD_NAME + "] ThuocTinh1 [" + drop.FLD_MAGIC0 + "] ThuocTinh2 [" + drop.FLD_MAGIC1 + "] ThuocTinh3 [" + drop.FLD_MAGIC2 + "] ThuocTinh4 [" + drop.FLD_MAGIC3 + "] ThuocTinh5 [" + drop.FLD_MAGIC4 + "]");
					}
					System.Buffer.BlockCopy(BitConverter.GetBytes(drop.FLD_MAGIC0), 0, array2, 0, 4);
					System.Buffer.BlockCopy(BitConverter.GetBytes(drop.FLD_MAGIC1), 0, array2, 4, 4);
					System.Buffer.BlockCopy(BitConverter.GetBytes(drop.FLD_MAGIC2), 0, array2, 8, 4);
					System.Buffer.BlockCopy(BitConverter.GetBytes(drop.FLD_MAGIC3), 0, array2, 12, 4);
					System.Buffer.BlockCopy(BitConverter.GetBytes(drop.FLD_MAGIC4), 0, array2, 16, 4);
					System.Buffer.BlockCopy(bytes, 0, array, 0, 4);
					System.Buffer.BlockCopy(array2, 0, array, 16, 20);
					System.Buffer.BlockCopy(BitConverter.GetBytes(drop.FLD_PID), 0, array, 8, 4);
					System.Buffer.BlockCopy(BitConverter.GetBytes(1), 0, array, 12, 4);
					if (drop.FLD_DAYS > 0 && drop.FLD_LEVEL1 <= Level && drop.FLD_LEVEL2 >= Level && drop.FLD_PP != 0 && drop.FLD_PP >= World.Random_So_Drop)
					{
						DateTime value2 = new(1970, 1, 1, 8, 0, 0);
						System.Buffer.BlockCopy(BitConverter.GetBytes((int)DateTime.Now.Subtract(value2).TotalSeconds), 0, array, 52, 4);
						System.Buffer.BlockCopy(BitConverter.GetBytes((int)DateTime.Now.AddDays(drop.FLD_DAYS).Subtract(value2).TotalSeconds), 0, array, 56, 4);
					}
					if (value.FLD_NJ > 0 && (value.FLD_RESIDE2 == 1 || value.FLD_RESIDE2 == 2 || value.FLD_RESIDE2 == 5 || value.FLD_RESIDE2 == 4 || value.FLD_RESIDE2 == 6))
					{
						System.Buffer.BlockCopy(BitConverter.GetBytes(1000), 0, array, 60, 2);
					}
				}
				catch (Exception ex)
				{
					LogHelper.WriteLine(LogLevel.Error, "Drop item 1 error: NPC: " + FLD_PID + "|" + Name + "   " + ex.Message);
					return null;
				}
				X_Mat_Dat_Vat_Pham_Loai x_Mat_Dat_Vat_Pham_Loai;
				X_Mat_Dat_Vat_Pham_Loai value3;
				try
				{
					if (FLD_BOSS == 0)
					{
						x_Mat_Dat_Vat_Pham_Loai = new(array, Rxjh_X, Rxjh_Y, Rxjh_Z, Rxjh_Map, yxqname, 0);
					}
					else
					{
						var num = RNG.Next(0, 2);
						var num2 = 0.0;
						var num3 = 0.0;
						if (FLD_PID == 16278)
						{
							num2 = RNG.Next(-50, 50);
							num3 = RNG.Next(-50, 50);
						}
						else
						{
							num2 = RNG.Next(-25, 25);
							num3 = RNG.Next(-25, 25);
						}
						var x = Rxjh_X + (float)((num != 0) ? (0.0 - num2) : num2);
						var y = Rxjh_Y + (float)((num != 0) ? (0.0 - num3) : num3);
						x_Mat_Dat_Vat_Pham_Loai = new(array, x, y, Rxjh_Z, Rxjh_Map, yxqname, 0);
					}
					if (x_Mat_Dat_Vat_Pham_Loai == null)
					{
						return null;
					}
					if (!World.ItmeTeM.TryGetValue(dBItmeId, out value3))
					{
						World.ItmeTeM.Add(dBItmeId, x_Mat_Dat_Vat_Pham_Loai);
					}
					x_Mat_Dat_Vat_Pham_Loai.GetARangeOfPlayersSendGroundIncreaseItemSoLieuPackage();
				}
				catch (Exception ex2)
				{
					LogHelper.WriteLine(LogLevel.Error, "Drop item 3 error: " + FLD_PID + "|" + Name + "   " + ex2.Message);
					return null;
				}
				try
				{
					if (World.ItmeTeM.TryGetValue(dBItmeId, out value3))
					{
						x_Mat_Dat_Vat_Pham_Loai.GetARangeOfPlayersSendGroundIncreaseItemSoLieuPackage();
					}
					return array;
				}
				catch (Exception ex3)
				{
					LogHelper.WriteLine(LogLevel.Error, "Drop item 4 error: " + FLD_PID + "|" + Name + " " + ex3.Message);
					return null;
				}
			}
			if (yxqname != null)
			{
				var parcelVacancy = yxqname.GetParcelVacancy(yxqname);
				if (parcelVacancy != -1)
				{
					yxqname.AddItems(bytes, BitConverter.GetBytes(drop.FLD_PID), parcelVacancy, BitConverter.GetBytes(1), new byte[56]);
				}
			}
			return null;
		}
		catch (Exception ex4)
		{
			LogHelper.WriteLine(LogLevel.Error, "Rơi ra vật phẩm bị lỗi !! " + FLD_PID + "|" + Name + " " + ex4.Message);
			return null;
		}
		finally
		{
			drop.FLD_PID = drop.FLD_PIDNew;
			drop.FLD_MAGIC0 = drop.FLD_MAGICNew0;
			drop.FLD_MAGIC1 = drop.FLD_MAGICNew1;
			drop.FLD_MAGIC2 = drop.FLD_MAGICNew2;
			drop.FLD_MAGIC3 = drop.FLD_MAGICNew3;
			drop.FLD_MAGIC4 = drop.FLD_MAGICNew4;
		}
	}

	public void BaoSuat_VatPham(Players yxqname)
	{
		try
		{
			if (Rxjh_Map == 801)
			{
				yxqname = null;
			}
			switch (FLD_BOSS)
			{
			case 0:
				BaoSuat_VatPham2(yxqname);
				break;
			case 1:
				Boss_BaoSuat_VatPham(yxqname);
				break;
			case 2:
				GSBaoSuat_VatPham(1, 9, yxqname);
				break;
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "BaoSuat VatPham error：" + ex);
		}
	}

	public void GSBaoSuat_VatPham(int sl, int maxsl, Players yxqname)
	{
		try
		{
			if (Rxjh_Exp <= 0)
			{
				return;
			}
			var gSDrop = DropClass.GetGSDrop(Level, sl, maxsl);
			if (gSDrop == null)
			{
				return;
			}
			foreach (var item in gSDrop)
			{
				if (item == null)
				{
					continue;
				}
				switch (item.FLD_PID)
				{
				case 800000002:
					if (item.FLD_MAGIC0 == 10)
					{
						var fLD_MAGIC11 = 0;
						var num11 = RNG.Next(1, 100);
						foreach (var item2 in item.DropShuX)
						{
							if (num11 <= item2.Max)
							{
								fLD_MAGIC11 = RNG.Next(item2.ShuXMin, item2.ShuXMax - 1);
								break;
							}
						}
						item.FLD_MAGIC0 = fLD_MAGIC11;
					}
					else if (item.FLD_MAGIC0 == 0)
					{
						item.FLD_MAGIC0 = World.GetValue(item.FLD_PID, 5);
					}
					break;
				case 800000001:
					if (item.FLD_MAGIC0 == 10)
					{
						var fLD_MAGIC7 = 0;
						var num7 = RNG.Next(1, 100);
						foreach (var item3 in item.DropShuX)
						{
							if (num7 <= item3.Max)
							{
								fLD_MAGIC7 = RNG.Next(item3.ShuXMin, item3.ShuXMax - 1);
								break;
							}
						}
						item.FLD_MAGIC0 = fLD_MAGIC7;
					}
					else if (item.FLD_MAGIC0 == 0)
					{
						item.FLD_MAGIC0 = World.GetValue(item.FLD_PID, 5);
					}
					break;
				case 800000023:
					if (item.FLD_MAGIC0 == 10)
					{
						var fLD_MAGIC9 = 0;
						var num9 = RNG.Next(1, 100);
						foreach (var item4 in item.DropShuX)
						{
							if (num9 <= item4.Max)
							{
								fLD_MAGIC9 = RNG.Next(item4.ShuXMin, item4.ShuXMax - 1);
								break;
							}
						}
						item.FLD_MAGIC0 = fLD_MAGIC9;
					}
					else if (item.FLD_MAGIC0 == 0)
					{
						item.FLD_MAGIC0 = World.GetValue(item.FLD_PID, 5);
					}
					break;
				case 800000024:
					if (item.FLD_MAGIC0 == 10)
					{
						var fLD_MAGIC6 = 0;
						var num6 = RNG.Next(1, 100);
						foreach (var item5 in item.DropShuX)
						{
							if (num6 <= item5.Max)
							{
								fLD_MAGIC6 = RNG.Next(item5.ShuXMin, item5.ShuXMax - 1);
								break;
							}
						}
						item.FLD_MAGIC0 = fLD_MAGIC6;
					}
					else if (item.FLD_MAGIC0 == 0)
					{
						item.FLD_MAGIC0 = World.GetValue(item.FLD_PID, 5);
					}
					break;
				case 800000025:
					if (item.FLD_MAGIC0 == 10)
					{
						var fLD_MAGIC12 = 0;
						var num12 = RNG.Next(1, 100);
						foreach (var item6 in item.DropShuX)
						{
							if (num12 <= item6.Max)
							{
								fLD_MAGIC12 = RNG.Next(item6.ShuXMin, item6.ShuXMax - 1);
								break;
							}
						}
						item.FLD_MAGIC0 = fLD_MAGIC12;
					}
					else if (item.FLD_MAGIC0 == 0)
					{
						item.FLD_MAGIC0 = World.GetValue(item.FLD_PID, 5);
					}
					break;
				case 800000026:
					if (item.FLD_MAGIC0 == 10)
					{
						var fLD_MAGIC3 = 0;
						var num3 = RNG.Next(1, 100);
						foreach (var item7 in item.DropShuX)
						{
							if (num3 <= item7.Max)
							{
								fLD_MAGIC3 = RNG.Next(item7.ShuXMin, item7.ShuXMax - 1);
								break;
							}
						}
						item.FLD_MAGIC0 = fLD_MAGIC3;
					}
					else if (item.FLD_MAGIC0 == 0)
					{
						item.FLD_MAGIC0 = World.GetValue(item.FLD_PID, 5);
					}
					break;
				case 800000028:
					item.FLD_MAGIC0 = World.GetValue(item.FLD_PID, 5);
					break;
				case 800000030:
					item.FLD_MAGIC0 = World.GetValue(800000030, 5);
					break;
				case 800000031:
					item.FLD_MAGIC0 = World.GetValue(800000031, 5);
					break;
				case 800000032:
					item.FLD_MAGIC0 = World.GetValue(800000032, 5);
					break;
				case 800000033:
					item.FLD_MAGIC0 = World.GetValue(800000033, 5);
					break;
				case 800000034:
					item.FLD_MAGIC0 = World.GetValue(800000034, 5);
					break;
				case 800000013:
					item.FLD_MAGIC0 = World.GetValue(item.FLD_PID, 5);
					break;
				case 800000047:
					if (item.FLD_MAGIC0 == 10)
					{
						var fLD_MAGIC8 = 0;
						var num8 = RNG.Next(1, 100);
						foreach (var item8 in item.DropShuX)
						{
							if (num8 <= item8.Max)
							{
								fLD_MAGIC8 = RNG.Next(item8.ShuXMin, item8.ShuXMax - 1);
								break;
							}
						}
						item.FLD_MAGIC0 = fLD_MAGIC8;
					}
					else if (item.FLD_MAGIC0 == 0)
					{
						item.FLD_MAGIC0 = RNG.Next(23, 51);
					}
					break;
				case 800000046:
					if (item.FLD_MAGIC0 == 0)
					{
						item.FLD_MAGIC0 = RNG.Next(1, 22);
					}
					break;
				case 1000000321:
					item.FLD_MAGIC0 = RNG.Next(1001, 2999);
					item.FLD_MAGIC1 = RNG.Next(10, 50);
					break;
				case 1000000323:
					item.FLD_MAGIC0 = RNG.Next(1001, 2999);
					item.FLD_MAGIC1 = RNG.Next(100, 150);
					break;
				case 1000000325:
					item.FLD_MAGIC0 = RNG.Next(1001, 2999);
					item.FLD_MAGIC1 = RNG.Next(400, 699);
					break;
				default:
				{
					if (item.FLD_MAGIC0 != 10)
					{
						break;
					}
					var fLD_MAGIC2 = 0;
					var num2 = RNG.Next(1, 100);
					foreach (var item9 in item.DropShuX)
					{
						if (num2 <= item9.Max)
						{
							fLD_MAGIC2 = RNG.Next(item9.ShuXMin, item9.ShuXMax - 1);
							break;
						}
					}
					item.FLD_MAGIC0 = fLD_MAGIC2;
					break;
				}
				case 1000000327:
					item.FLD_MAGIC0 = RNG.Next(1001, 2999);
					item.FLD_MAGIC1 = RNG.Next(2000, 2499);
					break;
				case 800000062:
					if (item.FLD_MAGIC0 == 10)
					{
						var fLD_MAGIC13 = 0;
						var num13 = RNG.Next(1, 100);
						foreach (var item10 in item.DropShuX)
						{
							if (num13 <= item10.Max)
							{
								fLD_MAGIC13 = RNG.Next(item10.ShuXMin, item10.ShuXMax - 1);
								break;
							}
						}
						item.FLD_MAGIC0 = fLD_MAGIC13;
					}
					else if (item.FLD_MAGIC0 == 0)
					{
						item.FLD_MAGIC0 = World.GetValue(item.FLD_PID, 5);
					}
					break;
				case 1000001651:
					if (item.FLD_MAGIC0 == 10)
					{
						var fLD_MAGIC10 = 0;
						var num10 = RNG.Next(1, 100);
						foreach (var item11 in item.DropShuX)
						{
							if (num10 <= item11.Max)
							{
								fLD_MAGIC10 = RNG.Next(item11.ShuXMin, item11.ShuXMax - 1);
								break;
							}
						}
						item.FLD_MAGIC0 = fLD_MAGIC10;
					}
					else if (item.FLD_MAGIC0 == 0)
					{
						item.FLD_MAGIC0 = World.GetValue(item.FLD_PID, 5);
					}
					break;
				case 800000061:
					if (item.FLD_MAGIC0 == 10)
					{
						var fLD_MAGIC5 = 0;
						var num5 = RNG.Next(1, 100);
						foreach (var item12 in item.DropShuX)
						{
							if (num5 <= item12.Max)
							{
								fLD_MAGIC5 = RNG.Next(item12.ShuXMin, item12.ShuXMax - 1);
								break;
							}
						}
						item.FLD_MAGIC0 = fLD_MAGIC5;
					}
					else if (item.FLD_MAGIC0 == 0)
					{
						item.FLD_MAGIC0 = World.GetValue(item.FLD_PID, 5);
					}
					break;
				case 1000001650:
					if (item.FLD_MAGIC0 == 10)
					{
						var fLD_MAGIC4 = 0;
						var num4 = RNG.Next(1, 100);
						foreach (var item13 in item.DropShuX)
						{
							if (num4 <= item13.Max)
							{
								fLD_MAGIC4 = RNG.Next(item13.ShuXMin, item13.ShuXMax - 1);
								break;
							}
						}
						item.FLD_MAGIC0 = fLD_MAGIC4;
					}
					else if (item.FLD_MAGIC0 == 0)
					{
						item.FLD_MAGIC0 = World.GetValue(item.FLD_PID, 5);
					}
					break;
				case 1000001620:
					if (item.FLD_MAGIC0 == 10)
					{
						var fLD_MAGIC = 0;
						var num = RNG.Next(1, 100);
						foreach (var item14 in item.DropShuX)
						{
							if (num <= item14.Max)
							{
								fLD_MAGIC = RNG.Next(item14.ShuXMin, item14.ShuXMax - 1);
								break;
							}
						}
						item.FLD_MAGIC0 = fLD_MAGIC;
					}
					else if (item.FLD_MAGIC0 == 0)
					{
						item.FLD_MAGIC0 = World.GetValue(item.FLD_PID, 5);
					}
					break;
				}
				RoiRaVatPham(item, yxqname);
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "BaoSuat VatPham 1 error：" + ex);
		}
	}

	public void Boss_BaoSuat_VatPham_DCH(Players yxqname)
	{
		try
		{
			if (Rxjh_Exp <= 0)
			{
				return;
			}
			var bossDrop_DCH = DropClass.GetBossDrop_DCH(Level);
			if (bossDrop_DCH == null)
			{
				return;
			}
			foreach (var item in bossDrop_DCH)
			{
				if (item != null)
				{
					RoiRaVatPham(item, yxqname);
				}
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "BaoSuat VatPham error：" + ex);
		}
	}

	public void Boss_BaoSuat_VatPham(Players playe)
	{
		try
		{
			if (Rxjh_Exp <= 0)
			{
				LogHelper.WriteLine(LogLevel.Error, "Lỗi [Rxjh_Exp = 0] nên Drop sẽ không rơi gì !!!, tăng [Rxjh_Exp] lên >= [1] trong [TBL_XWWL_MONSTER]");
				return;
			}
			if (FLD_BOSS == 1)
			{
				if (FLD_PID == 15100)
				{
					World.SoLuong_Item_DropBoss = 10;
				}
				else if (FLD_PID == 15236)
				{
					World.SoLuong_Item_DropBoss = 10;
				}
				else if (FLD_PID >= 15355 && FLD_PID <= 15356)
				{
					World.SoLuong_Item_DropBoss = 5;
				}
				else if (FLD_PID == 15403)
				{
					World.SoLuong_Item_DropBoss = 4;
				}
				else if (FLD_PID >= 15897 && FLD_PID <= 15899)
				{
					World.SoLuong_Item_DropBoss = 5;
				}
				else if (FLD_PID == 15900)
				{
					World.SoLuong_Item_DropBoss = RNG.Next(5, 10);
				}
				else if (FLD_PID == 15983)
				{
					World.SoLuong_Item_DropBoss = 100;
				}
				else if (Rxjh_Map == 1001 && FLD_PID == 16278)
				{
					World.SoLuong_Item_DropBoss = 50;
				}
				else if (Rxjh_Map == 101 && FLD_PID == 16278)
				{
					World.SoLuong_Item_DropBoss = 100;
				}
				else if (FLD_PID == 16320)
				{
					World.SoLuong_Item_DropBoss = 10;
				}
				else if (FLD_PID == 16321)
				{
					World.SoLuong_Item_DropBoss = 10;
				}
				else if (FLD_PID >= 16550 && FLD_PID <= 16554)
				{
					World.SoLuong_Item_DropBoss = 15;
				}
				else
				{
					World.SoLuong_Item_DropBoss = 15;
				}
			}
			var rate_Drop_Server = World.Rate_Drop_Server;
			if (playe != null)
			{
				if (playe.FLD_NhanVat_ThemVao_XacXuatRotVatPham_TiLePhanTram > 0.0)
				{
					rate_Drop_Server += (int)(rate_Drop_Server * playe.FLD_NhanVat_ThemVao_XacXuatRotVatPham_TiLePhanTram);
				}
				if (playe.QueryThienQuanDiaDoMap(playe.NhanVatToaDo_BanDo))
				{
					playe.GetTianguanBenefitBonus(1, playe.NhanVatToaDo_BanDo);
				}
			}
			var bossDrop = DropClass.GetBossDrop(Level);
			if (bossDrop == null)
			{
				LogHelper.WriteLine(LogLevel.Error, "Boss BaoSuat VatPham error：bossdrop == null [Vô giá trị] == :[" + playe.CharacterName + "]");
				return;
			}
			foreach (var item in bossDrop)
			{
				if (item != null)
				{
					switch (item.FLD_PID)
					{
					case 800000002:
						if (item.FLD_MAGIC0 == 10)
						{
							var fLD_MAGIC11 = 0;
							var num11 = RNG.Next(1, 100);
							foreach (var item2 in item.DropShuX)
							{
								if (num11 <= item2.Max)
								{
									fLD_MAGIC11 = RNG.Next(item2.ShuXMin, item2.ShuXMax - 1);
									break;
								}
							}
							item.FLD_MAGIC0 = fLD_MAGIC11;
						}
						else if (item.FLD_MAGIC0 == 0)
						{
							item.FLD_MAGIC0 = World.GetValue(item.FLD_PID, 4);
						}
						break;
					case 800000001:
						if (item.FLD_MAGIC0 == 10)
						{
							var fLD_MAGIC7 = 0;
							var num7 = RNG.Next(1, 100);
							foreach (var item3 in item.DropShuX)
							{
								if (num7 <= item3.Max)
								{
									fLD_MAGIC7 = RNG.Next(item3.ShuXMin, item3.ShuXMax - 1);
									break;
								}
							}
							item.FLD_MAGIC0 = fLD_MAGIC7;
						}
						else if (item.FLD_MAGIC0 == 0)
						{
							item.FLD_MAGIC0 = World.GetValue(item.FLD_PID, 4);
						}
						break;
					case 800000023:
						if (item.FLD_MAGIC0 == 10)
						{
							var fLD_MAGIC9 = 0;
							var num9 = RNG.Next(1, 100);
							foreach (var item4 in item.DropShuX)
							{
								if (num9 <= item4.Max)
								{
									fLD_MAGIC9 = RNG.Next(item4.ShuXMin, item4.ShuXMax - 1);
									break;
								}
							}
							item.FLD_MAGIC0 = fLD_MAGIC9;
						}
						else if (item.FLD_MAGIC0 == 0)
						{
							item.FLD_MAGIC0 = World.GetValue(item.FLD_PID, 4);
						}
						break;
					case 800000024:
						if (item.FLD_MAGIC0 == 10)
						{
							var fLD_MAGIC6 = 0;
							var num6 = RNG.Next(1, 100);
							foreach (var item5 in item.DropShuX)
							{
								if (num6 <= item5.Max)
								{
									fLD_MAGIC6 = RNG.Next(item5.ShuXMin, item5.ShuXMax - 1);
									break;
								}
							}
							item.FLD_MAGIC0 = fLD_MAGIC6;
						}
						else if (item.FLD_MAGIC0 == 0)
						{
							item.FLD_MAGIC0 = World.GetValue(item.FLD_PID, 4);
						}
						break;
					case 800000025:
						if (item.FLD_MAGIC0 == 10)
						{
							var fLD_MAGIC12 = 0;
							var num12 = RNG.Next(1, 100);
							foreach (var item6 in item.DropShuX)
							{
								if (num12 <= item6.Max)
								{
									fLD_MAGIC12 = RNG.Next(item6.ShuXMin, item6.ShuXMax - 1);
									break;
								}
							}
							item.FLD_MAGIC0 = fLD_MAGIC12;
						}
						else if (item.FLD_MAGIC0 == 0)
						{
							item.FLD_MAGIC0 = World.GetValue(item.FLD_PID, 4);
						}
						break;
					case 800000026:
						if (item.FLD_MAGIC0 == 10)
						{
							var fLD_MAGIC3 = 0;
							var num3 = RNG.Next(1, 100);
							foreach (var item7 in item.DropShuX)
							{
								if (num3 <= item7.Max)
								{
									fLD_MAGIC3 = RNG.Next(item7.ShuXMin, item7.ShuXMax - 1);
									break;
								}
							}
							item.FLD_MAGIC0 = fLD_MAGIC3;
						}
						else if (item.FLD_MAGIC0 == 0)
						{
							item.FLD_MAGIC0 = World.GetValue(item.FLD_PID, 4);
						}
						break;
					case 800000028:
						item.FLD_MAGIC0 = World.GetValue(item.FLD_PID, 4);
						break;
					case 800000030:
						item.FLD_MAGIC0 = World.GetValue(800000030, 4);
						break;
					case 800000031:
						item.FLD_MAGIC0 = World.GetValue(800000031, 4);
						break;
					case 800000032:
						item.FLD_MAGIC0 = World.GetValue(800000032, 4);
						break;
					case 800000033:
						item.FLD_MAGIC0 = World.GetValue(800000033, 4);
						break;
					case 800000034:
						item.FLD_MAGIC0 = World.GetValue(800000034, 4);
						break;
					case 800000013:
						item.FLD_MAGIC0 = World.GetValue(item.FLD_PID, 4);
						break;
					case 800000047:
						if (item.FLD_MAGIC0 == 10)
						{
							var fLD_MAGIC8 = 0;
							var num8 = RNG.Next(1, 100);
							foreach (var item8 in item.DropShuX)
							{
								if (num8 <= item8.Max)
								{
									fLD_MAGIC8 = RNG.Next(item8.ShuXMin, item8.ShuXMax - 1);
									break;
								}
							}
							item.FLD_MAGIC0 = fLD_MAGIC8;
						}
						else if (item.FLD_MAGIC0 == 0)
						{
							item.FLD_MAGIC0 = RNG.Next(23, 51);
						}
						break;
					case 800000046:
						if (item.FLD_MAGIC0 == 0)
						{
							item.FLD_MAGIC0 = RNG.Next(1, 22);
						}
						break;
					case 1000000321:
						item.FLD_MAGIC0 = RNG.Next(1001, 2999);
						item.FLD_MAGIC1 = RNG.Next(10, 50);
						break;
					case 1000000323:
						item.FLD_MAGIC0 = RNG.Next(1001, 2999);
						item.FLD_MAGIC1 = RNG.Next(100, 150);
						break;
					case 1000000325:
						item.FLD_MAGIC0 = RNG.Next(1001, 2999);
						item.FLD_MAGIC1 = RNG.Next(400, 699);
						break;
					default:
					{
						if (item.FLD_MAGIC0 != 10)
						{
							break;
						}
						var fLD_MAGIC2 = 0;
						var num2 = RNG.Next(1, 100);
						foreach (var item9 in item.DropShuX)
						{
							if (num2 <= item9.Max)
							{
								fLD_MAGIC2 = RNG.Next(item9.ShuXMin, item9.ShuXMax - 1);
								break;
							}
						}
						item.FLD_MAGIC0 = fLD_MAGIC2;
						break;
					}
					case 1000000327:
						item.FLD_MAGIC0 = RNG.Next(1001, 2999);
						item.FLD_MAGIC1 = RNG.Next(2000, 2499);
						break;
					case 800000062:
						if (item.FLD_MAGIC0 == 10)
						{
							var fLD_MAGIC13 = 0;
							var num13 = RNG.Next(1, 100);
							foreach (var item10 in item.DropShuX)
							{
								if (num13 <= item10.Max)
								{
									fLD_MAGIC13 = RNG.Next(item10.ShuXMin, item10.ShuXMax - 1);
									break;
								}
							}
							item.FLD_MAGIC0 = fLD_MAGIC13;
						}
						else if (item.FLD_MAGIC0 == 0)
						{
							item.FLD_MAGIC0 = World.GetValue(item.FLD_PID, 4);
						}
						break;
					case 1000001651:
						if (item.FLD_MAGIC0 == 10)
						{
							var fLD_MAGIC10 = 0;
							var num10 = RNG.Next(1, 100);
							foreach (var item11 in item.DropShuX)
							{
								if (num10 <= item11.Max)
								{
									fLD_MAGIC10 = RNG.Next(item11.ShuXMin, item11.ShuXMax - 1);
									break;
								}
							}
							item.FLD_MAGIC0 = fLD_MAGIC10;
						}
						else if (item.FLD_MAGIC0 == 0)
						{
							item.FLD_MAGIC0 = World.GetValue(item.FLD_PID, 4);
						}
						break;
					case 800000061:
						if (item.FLD_MAGIC0 == 10)
						{
							var fLD_MAGIC5 = 0;
							var num5 = RNG.Next(1, 100);
							foreach (var item12 in item.DropShuX)
							{
								if (num5 <= item12.Max)
								{
									fLD_MAGIC5 = RNG.Next(item12.ShuXMin, item12.ShuXMax - 1);
									break;
								}
							}
							item.FLD_MAGIC0 = fLD_MAGIC5;
						}
						else if (item.FLD_MAGIC0 == 0)
						{
							item.FLD_MAGIC0 = World.GetValue(item.FLD_PID, 4);
						}
						break;
					case 1000001650:
						if (item.FLD_MAGIC0 == 10)
						{
							var fLD_MAGIC4 = 0;
							var num4 = RNG.Next(1, 100);
							foreach (var item13 in item.DropShuX)
							{
								if (num4 <= item13.Max)
								{
									fLD_MAGIC4 = RNG.Next(item13.ShuXMin, item13.ShuXMax - 1);
									break;
								}
							}
							item.FLD_MAGIC0 = fLD_MAGIC4;
						}
						else if (item.FLD_MAGIC0 == 0)
						{
							item.FLD_MAGIC0 = World.GetValue(item.FLD_PID, 4);
						}
						break;
					case 1000001620:
						if (item.FLD_MAGIC0 == 10)
						{
							var fLD_MAGIC = 0;
							var num = RNG.Next(1, 100);
							foreach (var item14 in item.DropShuX)
							{
								if (num <= item14.Max)
								{
									fLD_MAGIC = RNG.Next(item14.ShuXMin, item14.ShuXMax - 1);
									break;
								}
							}
							item.FLD_MAGIC0 = fLD_MAGIC;
						}
						else if (item.FLD_MAGIC0 == 0)
						{
							item.FLD_MAGIC0 = World.GetValue(item.FLD_PID, 4);
						}
						break;
					}
					if (FLD_PID == 16278 && Rxjh_Map == World.ThanVoMon)
					{
						if (playe != null)
						{
							var parcelVacancy = playe.GetParcelVacancy(playe);
							if (parcelVacancy != -1)
							{
								playe.AddItem_ThuocTinh_int(item.FLD_PID, parcelVacancy, 1, item.FLD_MAGIC0, item.FLD_MAGIC1, item.FLD_MAGIC2, item.FLD_MAGIC3, item.FLD_MAGIC4, item.FLD_SoCapPhuHon, item.FLD_TrungCapPhuHon, item.FLD_TienHoa, item.FLD_KhoaLai, 0);
								World.ToanCucNhacNho("Thiên cơ các", 7, "Chúc mừng Đại hiệp [" + playe.CharacterName + "] tiêu diệt Ma Đầu nhận vật phẩm: [" + item.FLD_NAME + "].");
							}
							else
							{
								playe.HeThongNhacNho("Hành trang không còn chỗ trống!", 10, "Thiên cơ các");
							}
						}
					}
					else if (World.BatTat_NhatItem_UuTien_NhatTuDo_KoVutItem_Va_CuoiThu == 1)
					{
						RoiRaVatPham(item, playe);
					}
					else
					{
						RoiRaVatPham(item, playe);
					}
				}
				else
				{
					LogHelper.WriteLine(LogLevel.Error, "BossDropClass item vô giá trị == NULL !!!!!!!!!");
					var string_ = "Lỗi 123 [" + playe.AccountID + "][" + playe.CharacterName + "] 1:[" + FLD_PID + "] 2:[" + NPC_SessionID + "] 3:[" + Rxjh_Map + "] 4:[" + Level + "] 5:[" + Rxjh_Exp + "] 6:[" + bossDrop + "] 7:[" + item + "] 8:[" + playe.TeamID + "]";
					// logo.Log_Drop_BOSS_loi(string_, playe.UserName);
				}
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "BaoSuat VatPham error：" + ex);
		}
	}

	public static void GuiDi_TruocMat_BieuHien_ThaoPhat_PhoBan_QuaiVat(Dictionary<int, NpcClass> NpcList, Players player)
	{
		if (NpcList == null || NpcList.Count <= 0)
		{
			return;
		}
		foreach (var value in NpcList.Values)
		{
			if (value.FLD_PID == 16555 || value.FLD_PID == 16556)
			{
				continue;
			}
			if (value.NPCDeath)
			{
				var array = Converter.HexStringToByte("AA5536008E47012230008E4700000000000000000000000001000000A0400000000000004442000000000000000000000000000000000000000055AA");
				System.Buffer.BlockCopy(BitConverter.GetBytes(value.NPC_SessionID), 0, array, 4, 2);
				System.Buffer.BlockCopy(BitConverter.GetBytes(value.NPC_SessionID), 0, array, 10, 2);
				System.Buffer.BlockCopy(BitConverter.GetBytes(value.Rxjh_X), 0, array, 26, 4);
				System.Buffer.BlockCopy(BitConverter.GetBytes(value.Rxjh_Y), 0, array, 34, 4);
				System.Buffer.BlockCopy(BitConverter.GetBytes(value.怪物数字), 0, array, 46, 4);
				player.Client?.Send(array, array.Length);
			}
			else
			{
				var array2 = Converter.HexStringToByte("AA5526008E47052220008E47010000000000000034C20000000000006DC300000000000000000000000055AA");
				System.Buffer.BlockCopy(BitConverter.GetBytes(value.NPC_SessionID), 0, array2, 4, 2);
				System.Buffer.BlockCopy(BitConverter.GetBytes(value.NPC_SessionID), 0, array2, 10, 2);
				System.Buffer.BlockCopy(BitConverter.GetBytes(value.Rxjh_X), 0, array2, 18, 4);
				System.Buffer.BlockCopy(BitConverter.GetBytes(value.Rxjh_Y), 0, array2, 26, 4);
				player.Client?.Send(array2, array2.Length);
			}
		}
	}

	public void PhoBan_Event_FireDragon_StatusEffect()
	{
		Random random = new(DateTime.Now.Millisecond);
		var num = random.Next(1, 400);
		Rxjh_X = num - 200;
		num = random.Next(1, 400);
		Rxjh_Y = num - 400;
		var array = Converter.HexStringToByte("AA5536008E47012230008E4700000000000000000000000001000000A0400000000000004442000000000000000000000000000000000000000055AA");
		System.Buffer.BlockCopy(BitConverter.GetBytes(NPC_SessionID), 0, array, 4, 2);
		System.Buffer.BlockCopy(BitConverter.GetBytes(NPC_SessionID), 0, array, 10, 2);
		System.Buffer.BlockCopy(BitConverter.GetBytes(Rxjh_X), 0, array, 26, 4);
		System.Buffer.BlockCopy(BitConverter.GetBytes(Rxjh_Y), 0, array, 34, 4);
		if (FLD_PID == 16607 || FLD_PID == 16557)
		{
			num = random.Next(2, 5);
			System.Buffer.BlockCopy(BitConverter.GetBytes(num), 0, array, 46, 4);
			怪物数字 = num;
		}
		QuangBaSoLieu(array, array.Length);
		tem.Clear();
		NPCDeath = true;
		if (AutomaticAttack != null)
		{
			AutomaticAttack.Enabled = false;
		}
		if (AutomaticMove != null)
		{
			AutomaticMove.Enabled = true;
		}
		if (TuDongHoiSinh != null)
		{
			TuDongHoiSinh.Interval = FLD_NEWTIME * 1000;
			TuDongHoiSinh.Enabled = true;
		}
		else
		{
			TuDongHoiSinh = new(FLD_NEWTIME * 1000);
			TuDongHoiSinh.Elapsed += TuDongHoiSinhEvent;
			TuDongHoiSinh.Enabled = true;
		}
	}

	public void PhoBan_Event_FireDragon_StatusEffect_KetThuc(bool 是否玩家击杀)
	{
		NPCDeath = false;
		var array = Converter.HexStringToByte("AA5526008E47052220008E47010000000000000034C20000000000006DC300000000000000000000000055AA");
		System.Buffer.BlockCopy(BitConverter.GetBytes(NPC_SessionID), 0, array, 4, 2);
		System.Buffer.BlockCopy(BitConverter.GetBytes(NPC_SessionID), 0, array, 10, 2);
		System.Buffer.BlockCopy(BitConverter.GetBytes(Rxjh_X), 0, array, 18, 4);
		System.Buffer.BlockCopy(BitConverter.GetBytes(Rxjh_Y), 0, array, 26, 4);
		QuangBaSoLieu(array, array.Length);
		if (TuDongHoiSinh != null)
		{
			TuDongHoiSinh.Enabled = false;
			TuDongHoiSinh.Close();
			TuDongHoiSinh.Dispose();
			TuDongHoiSinh = null;
		}
		if (!是否玩家击杀)
		{
			ThaoPhat_PhoBan_QuaiVat_Time();
		}
	}

	public void ThaoPhat_PhoBan_QuaiVat_Time()
	{
		switch (FLD_PID)
		{
		case 16557:
		{
			List<Players> list = new();
			foreach (var value in World.allConnectedChars.Values)
			{
				if (value.NhanVatToaDo_BanDo == 43001 && World.是否讨伐副本危险区域(value) && !value.PlayerTuVong && value.副本复活剩余次数 > 0)
				{
					list.Add(value);
				}
			}
			if (list.Count <= 0)
			{
				break;
			}
			for (var i = 0; i < 怪物数字; i++)
			{
				Random random = new(DateTime.Now.Millisecond);
				var index = random.Next(0, list.Count - 1);
				var players = list[index];
				players.NhanVat_HP = 0;
				players.Death();
				PlayCw = null;
				Play_null();
				players.CapNhat_HP_MP_SP();
				list.Remove(players);
				if (list.Count == 0)
				{
					break;
				}
			}
			break;
		}
		case 16600:
		{
			foreach (var value2 in World.allConnectedChars.Values)
			{
				if (!FindPlayers(100, value2) && World.是否讨伐副本危险区域(value2) && value2.副本复活剩余次数 > 0)
				{
					GuiDi_DiDongSoLieu(Rxjh_cs_X, Rxjh_cs_Y, 50, 1);
					value2.NhanVat_HP = 0;
					value2.Death();
					PlayCw = null;
					Play_null();
					value2.CapNhat_HP_MP_SP();
					World.讨伐副本添加怪物();
				}
			}
			break;
		}
		case 16602:
		{
			foreach (var value3 in World.allConnectedChars.Values)
			{
				if (FindPlayers(100, value3) && value3.副本复活剩余次数 > 0)
				{
					GuiDi_DiDongSoLieu(Rxjh_cs_X, Rxjh_cs_Y, 50, 1);
					value3.NhanVat_HP = 0;
					value3.Death();
					PlayCw = null;
					Play_null();
					value3.CapNhat_HP_MP_SP();
				}
			}
			break;
		}
		case 16604:
		{
			foreach (var value4 in World.allConnectedChars.Values)
			{
				if (FindPlayers(80, value4) && value4.副本复活剩余次数 > 0)
				{
					GuiDi_DiDongSoLieu(Rxjh_cs_X, Rxjh_cs_Y, 50, 1);
					value4.NhanVat_HP = 0;
					value4.Death();
					PlayCw = null;
					Play_null();
					value4.CapNhat_HP_MP_SP();
				}
			}
			break;
		}
		case 16607:
			if (NPCDeath)
			{
				break;
			}
			{
				foreach (var value5 in World.allConnectedChars.Values)
				{
					if (World.是否讨伐副本危险区域(value5) && !value5.PlayerTuVong && value5.副本复活剩余次数 > 0)
					{
						GuiDi_DiDongSoLieu(Rxjh_cs_X, Rxjh_cs_Y, 50, 1);
						value5.NhanVat_HP = 0;
						value5.Death();
						PlayCw = null;
						Play_null();
						value5.CapNhat_HP_MP_SP();
					}
				}
				break;
			}
		}
	}

	public void BaoSuat_VatPham2(Players play)
	{
		try
		{
			if (Rxjh_Exp <= 0)
			{
				return;
			}
			var num = RNG.Next(1, 8000);
			var rate_Drop_Server = World.Rate_Drop_Server;
			if (World.Gioi_han_EXP_khi_co_TLC == 1 && ThoiGian_KetThuc_Rate_Exp_Drop_Gold())
			{
				rate_Drop_Server = 0;
				return;
			}
			if (play.Player_Level < 159)
			{
				if (play.Player_Level < 159)
				{
					rate_Drop_Server = World.Rate_Drop_Server;
				}
				else
				{
					var num2 = play.Player_Level - Level;
					if (play.Player_Level > Level)
					{
						rate_Drop_Server = (int)(rate_Drop_Server / (0.5 + num2) + 2.0);
					}
					else if (play.Player_Level == Level)
					{
						rate_Drop_Server = (int)(rate_Drop_Server / 2.0);
					}
					else
					{
						var num3 = Level - play.Player_Level;
						rate_Drop_Server = ((num3 == 1) ? ((int)(rate_Drop_Server / World.Drop_Cach_1_Level_Quai)) : ((num3 == 2) ? ((int)(rate_Drop_Server / World.Drop_Cach_2_Level_Quai)) : ((num3 == 3) ? ((int)(rate_Drop_Server / World.Drop_Cach_3_Level_Quai)) : ((num3 == 4) ? ((int)(rate_Drop_Server / World.Drop_Cach_4_Level_Quai)) : ((num3 == 5) ? ((int)(rate_Drop_Server / World.Drop_Cach_5_Level_Quai)) : ((num3 == 6) ? ((int)(rate_Drop_Server / World.Drop_Cach_6_Level_Quai)) : ((num3 == 7) ? ((int)(rate_Drop_Server / World.Drop_Cach_7_Level_Quai)) : ((num3 == 8) ? ((int)(rate_Drop_Server / World.Drop_Cach_8_Level_Quai)) : ((num3 == 9) ? ((int)(rate_Drop_Server / World.Drop_Cach_9_Level_Quai)) : ((num3 >= 10 && num3 <= 14) ? ((int)(rate_Drop_Server / World.Drop_Cach_10_Level_Quai)) : ((num3 >= 15 && num3 <= 19) ? ((int)(rate_Drop_Server / World.Drop_Cach_15_Level_Quai)) : ((num3 == 20) ? ((int)(rate_Drop_Server / World.Drop_Cach_20_Level_Quai)) : 0))))))))))));
					}
					if (play.Player_Level >= 100 && play.Player_Level < 115)
					{
						rate_Drop_Server = (int)(rate_Drop_Server / World.Giam_Gold_Drop_Level_100_114);
					}
					else if (play.Player_Level >= 115 && play.Player_Level < 120)
					{
						rate_Drop_Server = (int)(rate_Drop_Server / World.Giam_Gold_Drop_Level_115_119);
					}
					else if (play.Player_Level >= 120 && play.Player_Level < 125)
					{
						rate_Drop_Server = (int)(rate_Drop_Server / World.Giam_Gold_Drop_Level_120_124);
					}
					else if (play.Player_Level >= 125 && play.Player_Level < 129)
					{
						rate_Drop_Server = (int)(rate_Drop_Server / World.Giam_Gold_Drop_Level_125_128);
					}
					else if (play.Player_Level >= 129 && play.Player_Level < 131)
					{
						rate_Drop_Server = (int)(rate_Drop_Server / World.Giam_Gold_Drop_Level_129_130);
					}
				}
			}
			else
			{
				var num4 = play.Player_Level - Level;
				if (play.Player_Level > Level)
				{
					rate_Drop_Server = (int)(rate_Drop_Server / (0.5 + num4) + 3.0);
				}
				else if (play.Player_Level == Level)
				{
					rate_Drop_Server = (int)(rate_Drop_Server / 2.0);
				}
				else
				{
					switch (Level - play.Player_Level)
					{
					case 1:
						rate_Drop_Server = (int)(rate_Drop_Server * World.Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_1);
						break;
					case 2:
						rate_Drop_Server = (int)(rate_Drop_Server * World.Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_2);
						break;
					case 3:
						rate_Drop_Server = (int)(rate_Drop_Server * World.Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_3);
						break;
					case 4:
						rate_Drop_Server = (int)(rate_Drop_Server * World.Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_4);
						break;
					case 5:
						rate_Drop_Server = (int)(rate_Drop_Server * World.Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_5);
						break;
					case 6:
						rate_Drop_Server = (int)(rate_Drop_Server * World.Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_6);
						break;
					case 7:
						rate_Drop_Server = (int)(rate_Drop_Server * World.Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_7);
						break;
					case 8:
						rate_Drop_Server = (int)(rate_Drop_Server * World.Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_8);
						break;
					case 9:
						rate_Drop_Server = (int)(rate_Drop_Server * World.Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_9);
						break;
					case 10:
						rate_Drop_Server = (int)(rate_Drop_Server * World.Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_10);
						break;
					case 11:
						rate_Drop_Server = (int)(rate_Drop_Server * World.Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_11);
						break;
					case 12:
						rate_Drop_Server = (int)(rate_Drop_Server * World.Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_12);
						break;
					case 13:
						rate_Drop_Server = (int)(rate_Drop_Server * World.Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_13);
						break;
					case 14:
						rate_Drop_Server = (int)(rate_Drop_Server * World.Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_14);
						break;
					case 15:
						rate_Drop_Server = (int)(rate_Drop_Server * World.Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_15);
						break;
					case 16:
						rate_Drop_Server = (int)(rate_Drop_Server * World.Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_16);
						break;
					case 17:
						rate_Drop_Server = (int)(rate_Drop_Server * World.Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_17);
						break;
					case 18:
						rate_Drop_Server = (int)(rate_Drop_Server * World.Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_18);
						break;
					case 19:
						rate_Drop_Server = (int)(rate_Drop_Server * World.Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_19);
						break;
					case 20:
						rate_Drop_Server = (int)(rate_Drop_Server * World.Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_20);
						break;
					default:
						rate_Drop_Server = 0;
						return;
					}
				}
			}
			if (play.TitleDrug.ContainsKey(1008001511) || play.TitleDrug.ContainsKey(1008002101))
			{
				rate_Drop_Server = (int)(rate_Drop_Server * World.Bonus_Drop_BanDo_TDKH);
			}
			if (World.KhuLuyenTap_PK_Event != null && play.NhanVatToaDo_BanDo >= World.KhuLuyenTap1 && play.NhanVatToaDo_BanDo <= World.KhuLuyenTap9)
			{
				rate_Drop_Server += 1000;
			}
			if (play != null)
			{
				if (play.Player_Level <= World.X2BaoSuat_CapDo_GioiHanCaoNhat)
				{
					rate_Drop_Server *= (int)World.X2CapDo_GioiHanCaoNhat_BoiSo;
				}
				if (play.FLD_NhanVat_ThemVao_XacXuatRotVatPham_TiLePhanTram > 0.0)
				{
					rate_Drop_Server += (int)(rate_Drop_Server * play.FLD_NhanVat_ThemVao_XacXuatRotVatPham_TiLePhanTram);
				}
				if (play.QueryThienQuanDiaDoMap(play.NhanVatToaDo_BanDo))
				{
					rate_Drop_Server += (int)play.GetTianguanBenefitBonus(1, play.NhanVatToaDo_BanDo);
				}
			}
			if (num > rate_Drop_Server)
			{
				return;
			}
			DropClass drop;
			if (World.allConnectedChars.TryGetValue(play.SessionID, out var value))
			{
				if (value.FLD_VIP != 0)
				{
					return;
				}
				drop = DropClass.GetDrop(Level);
				if (drop == null)
				{
					return;
				}
			}
			else
			{
				drop = DropClass.GetDrop(Level);
				if (drop == null)
				{
					return;
				}
			}
			switch (drop.FLD_PID)
			{
			case 800000002:
				if (drop.FLD_MAGIC0 == 10)
				{
					var fLD_MAGIC11 = 0;
					var num15 = RNG.Next(1, 100);
					foreach (var item in drop.DropShuX)
					{
						if (num15 <= item.Max)
						{
							fLD_MAGIC11 = RNG.Next(item.ShuXMin, item.ShuXMax - 1);
							break;
						}
					}
					drop.FLD_MAGIC0 = fLD_MAGIC11;
				}
				else if (drop.FLD_MAGIC0 == 0)
				{
					drop.FLD_MAGIC0 = World.GetValue(drop.FLD_PID, 3);
				}
				break;
			case 800000001:
				if (drop.FLD_MAGIC0 == 10)
				{
					var fLD_MAGIC7 = 0;
					var num11 = RNG.Next(1, 100);
					foreach (var item2 in drop.DropShuX)
					{
						if (num11 <= item2.Max)
						{
							fLD_MAGIC7 = RNG.Next(item2.ShuXMin, item2.ShuXMax - 1);
							break;
						}
					}
					drop.FLD_MAGIC0 = fLD_MAGIC7;
				}
				else if (drop.FLD_MAGIC0 == 0)
				{
					drop.FLD_MAGIC0 = World.GetValue(drop.FLD_PID, 3);
				}
				break;
			case 800000023:
				if (drop.FLD_MAGIC0 == 10)
				{
					var fLD_MAGIC9 = 0;
					var num13 = RNG.Next(1, 100);
					foreach (var item3 in drop.DropShuX)
					{
						if (num13 <= item3.Max)
						{
							fLD_MAGIC9 = RNG.Next(item3.ShuXMin, item3.ShuXMax - 1);
							break;
						}
					}
					drop.FLD_MAGIC0 = fLD_MAGIC9;
				}
				else if (drop.FLD_MAGIC0 == 0)
				{
					drop.FLD_MAGIC0 = World.GetValue(drop.FLD_PID, 3);
				}
				break;
			case 800000024:
				if (drop.FLD_MAGIC0 == 10)
				{
					var fLD_MAGIC6 = 0;
					var num10 = RNG.Next(1, 100);
					foreach (var item4 in drop.DropShuX)
					{
						if (num10 <= item4.Max)
						{
							fLD_MAGIC6 = RNG.Next(item4.ShuXMin, item4.ShuXMax - 1);
							break;
						}
					}
					drop.FLD_MAGIC0 = fLD_MAGIC6;
				}
				else if (drop.FLD_MAGIC0 == 0)
				{
					drop.FLD_MAGIC0 = World.GetValue(drop.FLD_PID, 3);
				}
				break;
			case 800000025:
				if (drop.FLD_MAGIC0 == 10)
				{
					var fLD_MAGIC12 = 0;
					var num16 = RNG.Next(1, 100);
					foreach (var item5 in drop.DropShuX)
					{
						if (num16 <= item5.Max)
						{
							fLD_MAGIC12 = RNG.Next(item5.ShuXMin, item5.ShuXMax - 1);
							break;
						}
					}
					drop.FLD_MAGIC0 = fLD_MAGIC12;
				}
				else if (drop.FLD_MAGIC0 == 0)
				{
					drop.FLD_MAGIC0 = World.GetValue(drop.FLD_PID, 3);
				}
				break;
			case 800000026:
				if (drop.FLD_MAGIC0 == 10)
				{
					var fLD_MAGIC3 = 0;
					var num7 = RNG.Next(1, 100);
					foreach (var item6 in drop.DropShuX)
					{
						if (num7 <= item6.Max)
						{
							fLD_MAGIC3 = RNG.Next(item6.ShuXMin, item6.ShuXMax - 1);
							break;
						}
					}
					drop.FLD_MAGIC0 = fLD_MAGIC3;
				}
				else if (drop.FLD_MAGIC0 == 0)
				{
					drop.FLD_MAGIC0 = World.GetValue(drop.FLD_PID, 3);
				}
				break;
			case 800000028:
				drop.FLD_MAGIC0 = World.GetValue(drop.FLD_PID, 3);
				break;
			case 800000030:
				drop.FLD_MAGIC0 = World.GetValue(800000030, 3);
				break;
			case 800000031:
				drop.FLD_MAGIC0 = World.GetValue(800000031, 3);
				break;
			case 800000032:
				drop.FLD_MAGIC0 = World.GetValue(800000032, 3);
				break;
			case 800000033:
				drop.FLD_MAGIC0 = World.GetValue(800000033, 3);
				break;
			case 800000034:
				drop.FLD_MAGIC0 = World.GetValue(800000034, 3);
				break;
			case 800000013:
				drop.FLD_MAGIC0 = World.GetValue(drop.FLD_PID, 3);
				break;
			case 800000047:
				if (drop.FLD_MAGIC0 == 10)
				{
					var fLD_MAGIC8 = 0;
					var num12 = RNG.Next(1, 100);
					foreach (var item7 in drop.DropShuX)
					{
						if (num12 <= item7.Max)
						{
							fLD_MAGIC8 = RNG.Next(item7.ShuXMin, item7.ShuXMax - 1);
							break;
						}
					}
					drop.FLD_MAGIC0 = fLD_MAGIC8;
				}
				else if (drop.FLD_MAGIC0 == 0)
				{
					drop.FLD_MAGIC0 = RNG.Next(23, 51);
				}
				break;
			case 800000046:
				if (drop.FLD_MAGIC0 == 0)
				{
					drop.FLD_MAGIC0 = RNG.Next(1, 22);
				}
				break;
			case 1000000321:
				drop.FLD_MAGIC0 = RNG.Next(1001, 2999);
				drop.FLD_MAGIC1 = RNG.Next(10, 50);
				break;
			case 1000000323:
				drop.FLD_MAGIC0 = RNG.Next(1001, 2999);
				drop.FLD_MAGIC1 = RNG.Next(100, 150);
				break;
			case 1000000325:
				drop.FLD_MAGIC0 = RNG.Next(1001, 2999);
				drop.FLD_MAGIC1 = RNG.Next(400, 699);
				break;
			default:
			{
				if (drop.FLD_MAGIC0 != 10)
				{
					break;
				}
				var fLD_MAGIC2 = 0;
				var num6 = RNG.Next(1, 100);
				foreach (var item8 in drop.DropShuX)
				{
					if (num6 <= item8.Max)
					{
						fLD_MAGIC2 = RNG.Next(item8.ShuXMin, item8.ShuXMax - 1);
						break;
					}
				}
				drop.FLD_MAGIC0 = fLD_MAGIC2;
				break;
			}
			case 1000000327:
				drop.FLD_MAGIC0 = RNG.Next(1001, 2999);
				drop.FLD_MAGIC1 = RNG.Next(2000, 2499);
				break;
			case 800000062:
				if (drop.FLD_MAGIC0 == 10)
				{
					var fLD_MAGIC13 = 0;
					var num17 = RNG.Next(1, 100);
					foreach (var item9 in drop.DropShuX)
					{
						if (num17 <= item9.Max)
						{
							fLD_MAGIC13 = RNG.Next(item9.ShuXMin, item9.ShuXMax - 1);
							break;
						}
					}
					drop.FLD_MAGIC0 = fLD_MAGIC13;
				}
				else if (drop.FLD_MAGIC0 == 0)
				{
					drop.FLD_MAGIC0 = World.GetValue(drop.FLD_PID, 3);
				}
				break;
			case 1000001651:
				if (drop.FLD_MAGIC0 == 10)
				{
					var fLD_MAGIC10 = 0;
					var num14 = RNG.Next(1, 100);
					foreach (var item10 in drop.DropShuX)
					{
						if (num14 <= item10.Max)
						{
							fLD_MAGIC10 = RNG.Next(item10.ShuXMin, item10.ShuXMax - 1);
							break;
						}
					}
					drop.FLD_MAGIC0 = fLD_MAGIC10;
				}
				else if (drop.FLD_MAGIC0 == 0)
				{
					drop.FLD_MAGIC0 = World.GetValue(drop.FLD_PID, 3);
				}
				break;
			case 800000061:
				if (drop.FLD_MAGIC0 == 10)
				{
					var fLD_MAGIC5 = 0;
					var num9 = RNG.Next(1, 100);
					foreach (var item11 in drop.DropShuX)
					{
						if (num9 <= item11.Max)
						{
							fLD_MAGIC5 = RNG.Next(item11.ShuXMin, item11.ShuXMax - 1);
							break;
						}
					}
					drop.FLD_MAGIC0 = fLD_MAGIC5;
				}
				else if (drop.FLD_MAGIC0 == 0)
				{
					drop.FLD_MAGIC0 = World.GetValue(drop.FLD_PID, 3);
				}
				break;
			case 1000001650:
				if (drop.FLD_MAGIC0 == 10)
				{
					var fLD_MAGIC4 = 0;
					var num8 = RNG.Next(1, 100);
					foreach (var item12 in drop.DropShuX)
					{
						if (num8 <= item12.Max)
						{
							fLD_MAGIC4 = RNG.Next(item12.ShuXMin, item12.ShuXMax - 1);
							break;
						}
					}
					drop.FLD_MAGIC0 = fLD_MAGIC4;
				}
				else if (drop.FLD_MAGIC0 == 0)
				{
					drop.FLD_MAGIC0 = World.GetValue(drop.FLD_PID, 3);
				}
				break;
			case 1000001620:
				if (drop.FLD_MAGIC0 == 10)
				{
					var fLD_MAGIC = 0;
					var num5 = RNG.Next(1, 100);
					foreach (var item13 in drop.DropShuX)
					{
						if (num5 <= item13.Max)
						{
							fLD_MAGIC = RNG.Next(item13.ShuXMin, item13.ShuXMax - 1);
							break;
						}
					}
					drop.FLD_MAGIC0 = fLD_MAGIC;
				}
				else if (drop.FLD_MAGIC0 == 0)
				{
					drop.FLD_MAGIC0 = World.GetValue(drop.FLD_PID, 3);
				}
				break;
			}
			RoiRaVatPham(drop, play);
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "BaoSuat VatPham error：" + ex);
		}
	}

	public void AbnormalStatusList()
	{
		if (TrangThai_BatThuong == null || TrangThai_BatThuong.Count == 0)
		{
			return;
		}
		try
		{
			var queue = Queue.Synchronized(new());
			foreach (var value in TrangThai_BatThuong.Values)
			{
				queue.Enqueue(value);
			}
			while (queue.Count > 0)
			{
				var x_Di_Thuong_Trang_Thai_Loai = (X_Di_Thuong_Trang_Thai_Loai)queue.Dequeue();
				x_Di_Thuong_Trang_Thai_Loai.ThoiGianKetThucSuKien();
				TrangThai_BatThuong?.Remove(x_Di_Thuong_Trang_Thai_Loai.FLD_PID);
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "MPC TrangThai_BatThuong Danh sách error: [" + NPC_SessionID + "]-[" + Name + "]" + ex.Message);
		}
	}

	public void EndAbnormalBloodDropStatusList()
	{
		if (TrangThai_MatMau_BatThuong == null)
		{
			return;
		}
		var queue = Queue.Synchronized(new());
		try
		{
			foreach (var value in TrangThai_MatMau_BatThuong.Values)
			{
				queue.Enqueue(value);
			}
			while (queue.Count > 0)
			{
				if (World.jlMsg == 1)
				{
					LogHelper.WriteLine(0, "TrangThai_MatMau_BatThuong Danh sách");
				}
				var x_Di_Thuong_Mat_Mau_Trang_Thai_Loai = (X_Di_Thuong_Mat_Mau_Trang_Thai_Loai)queue.Dequeue();
				x_Di_Thuong_Mat_Mau_Trang_Thai_Loai.ThoiGianKetThucSuKien();
				TrangThai_MatMau_BatThuong?.Remove(x_Di_Thuong_Mat_Mau_Trang_Thai_Loai.FLD_PID);
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "MPC TrangThai_MatMau_BatThuong Liệt kê danh sách error: [" + NPC_SessionID + "]-[" + Name + "]" + ex.Message);
		}
		finally
		{
			queue = null;
		}
	}

	public void GuiDi_QuaiVat_TrenDau_DoTieu(int 是否消失)
	{
		if (World.jlMsg == 1)
		{
			LogHelper.WriteLine(0, "NpcClass_ Gửi đi quái vật trên đầu đồ tiêu");
		}
		var string_ = "AA551200E9478B100C0046050000E94700000100000055AA";
		var array = Converter.HexStringToByte(string_);
		System.Buffer.BlockCopy(BitConverter.GetBytes(NPC_SessionID), 0, array, 14, 4);
		System.Buffer.BlockCopy(BitConverter.GetBytes(是否消失), 0, array, 18, 4);
		QuangBaSoLieu(array, array.Length);
	}

	public void GuiDi_DameLenNguoi(int CongKichLuc, int NhanVat_ID, int Quai_ID)
	{
		var array = Converter.HexStringToByte("AA551200A42789000C002C0100000F0000000100000055AA");
		System.Buffer.BlockCopy(BitConverter.GetBytes(Quai_ID), 0, array, 4, 2);
		System.Buffer.BlockCopy(BitConverter.GetBytes(NhanVat_ID), 0, array, 10, 2);
		System.Buffer.BlockCopy(BitConverter.GetBytes(CongKichLuc), 0, array, 18, 2);
		QuangBaSoLieu(array, array.Length);
	}

	public bool LookInNpc(int far_, NpcClass Npc)
	{
		if (Npc.Rxjh_Map != Rxjh_Map)
		{
			return false;
		}
		var num = Npc.Rxjh_X - Rxjh_X;
		var num2 = Npc.Rxjh_Y - Rxjh_Y;
		return (int)Math.Sqrt(num * (double)num + num2 * (double)num2) <= (double)far_;
	}

	public bool FindPlayerByRange(int far_, Players Playe)
	{
		if (Playe.NhanVatToaDo_BanDo != Rxjh_Map)
		{
			return false;
		}
		if (Playe.NhanVatToaDo_BanDo == 7101)
		{
			far_ = 1000;
		}
		var num = Playe.NhanVatToaDo_X - Rxjh_X;
		var num2 = Playe.NhanVatToaDo_Y - Rxjh_Y;
		return (int)Math.Sqrt(num * (double)num + num2 * (double)num2) <= (double)far_;
	}

	public bool FindPlayers(int far_, X_Linh_Thu_Loai Playe)
	{
		if (Playe.NhanVatToaDo_MAP != Rxjh_Map)
		{
			return false;
		}
		var num = Playe.NhanVatToaDo_X - Rxjh_X;
		var num2 = Playe.NhanVatToaDo_Y - Rxjh_Y;
		return (int)Math.Sqrt(num * (double)num + num2 * (double)num2) <= (double)far_;
	}

	public void npc_Add(Players payer)
	{
		var num = 0;
		try
		{
			num = 1;
			if (payer != null)
			{
				num = 2;
				if (payer.Monster_AttackList == null)
				{
					num = 3;
					payer.Monster_AttackList = new();
					num = 4;
					payer.Monster_AttackList.Add(NPC_SessionID, this);
				}
				else if (payer == null || payer.Monster_AttackList == null || !payer.Monster_AttackList.ContainsKey(NPC_SessionID))
				{
					num = 5;
					payer.Monster_AttackList.Add(NPC_SessionID, this);
				}
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Dẫn quái Phạm Lỗi tại num：[" + num + "]-[" + payer.AccountID + "]-[" + payer.CharacterName + "] - " + ex.Message);
		}
	}

	public bool QuaiVatKhoangCach_KiemTra(Players npcTemp)
	{
		var num = Rxjh_X - npcTemp.NhanVatToaDo_X;
		var num2 = Rxjh_Y - npcTemp.NhanVatToaDo_Y;
		var num3 = (float)Math.Sqrt(num * num + num2 * num2);
		if (num3 >= 100f || Rxjh_Map != npcTemp.NhanVatToaDo_BanDo || NPCDeath || Rxjh_HP <= 0)
		{
			return true;
		}
		return false;
	}

	public void HutQuai_ThanhLy(Players payer)
	{
		try
		{
			if (payer == null)
			{
				return;
			}
			List<NpcClass> list = new();
			foreach (var value in payer.Monster_AttackList.Values)
			{
				list.Add(value);
			}
			for (var i = 0; i < list.Count; i++)
			{
				if (list[i].QuaiVatKhoangCach_KiemTra(payer))
				{
					payer.Monster_AttackList.RemoveSafe(list[i].NPC_SessionID);
				}
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Hút quái thanh lý bị lỗi 11 !! - " + ex.Message);
		}
	}

	public bool GetRangePlayers()
	{
		try
		{
			Players players = null;
			if (PlayList != null)
			{
				foreach (var value in PlayList.Values)
				{
					HutQuai_ThanhLy(value);
					if (players == null && value.NhanVat_HP > 0 && FindPlayers(100, value))
					{
						if (value.FLD_NhanVatCoBan_PhongNgu > 0 && value.Monster_AttackList.Count < World.Hut_Quai_SoLuong)
						{
							players = value;
						}
					}
					else if (value.NhanVat_HP > 0 && FindPlayers(100, value) && players.FLD_NhanVatCoBan_PhongNgu > value.FLD_NhanVatCoBan_PhongNgu && value.Monster_AttackList.Count < World.Hut_Quai_SoLuong)
					{
						players = value;
					}
				}
				if (players != null)
				{
					players_quaidanh = players;
					npc_Add(players);
					Play_Add(players, 0);
					return true;
				}
			}
		}
		catch (Exception)
		{
			return false;
		}
		return false;
	}

	public void QuangBaSoLieu(byte[] data, int length)
	{
		try
		{
			if (this.CurrentZone != null && this.CurrentZone.IsCrossServer)
			{
				// Nếu là zone liên server, gọi phương thức mở rộng
				SendCurrentRangeBroadcastDataCrossServer(data,length);
				return;
			}
			if (PlayList == null)
			{
				return;
			}
			foreach (var value2 in PlayList.Values)
			{
				if (value2.Client != null)
				{
					if (value2.Client.Running)
					{
						value2.Client.Send_Map_Data(data, length);
					}
					else
					{
						value2.Client.Dispose();
						PlayList.Remove(value2.SessionID);
					}
				}
				if (!World.allConnectedChars.TryGetValue(value2.Client.SessionID, out Players _))
				{
					LogHelper.WriteLine(LogLevel.Error, "NPC QuangBa SoLieu Xóa bỏ số thẻ nhân vật 111 ：[" + value2.AccountID + "]   [" + value2.CharacterName + "]");
					if (value2.Client != null)
					{
						value2.Client.Dispose();
						LogHelper.WriteLine(LogLevel.Error, "Disconnected![Mã dis 06]");
						LogHelper.WriteLine(LogLevel.Error,"Disconnected![Mã dis 06]");
					}
					PlayList.Remove(value2.SessionID);
				}
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "NPC QuangBa SoLieu 333 error2：" + ex);
		}
	}

}
